entrypoint = "main.py"
modules = ["python-3.11"]

[nix]
channel = "stable-24_05"
packages = ["espeak-ng", "ffmpeg-full", "procps"]

[env]
PYTHONPATH = "${REPL_HOME}"
PYTHONUNBUFFERED = "1"
# Variables para detección de Replit
REPL_ENVIRONMENT = "true"
# Variables para el sistema de cifrado
CRYPTO_AVAILABLE = "true"

[unitTest]
language = "python3"

[gitHubImport]
requiredFiles = [".replit", "replit.nix"]

[deployment]
run = ["python3", "main.py"]
deploymentTarget = "cloudrun"

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 8080
externalPort = 80

[workflows]
runButton = "Discord Bot"

[[workflows.workflow]]
name = "Discord Bot"
author = 44334379
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python main.py"
