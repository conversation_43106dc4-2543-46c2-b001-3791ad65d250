#!/usr/bin/env python3
"""
Módulo de gestión de configuración del bot
Maneja configuraciones locales y sincronización con GitHub
"""

import json
import os
import aiohttp
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List

class BotConfigManager:
    def __init__(self, github_token: str = None, repo_owner: str = "Fl0zWer", repo_name: str = "Paimon"):
        self.github_token = github_token
        self.repo_owner = repo_owner
        self.repo_name = repo_name
        self.config_file_path = "PaimonCacheConfig.js"
        self.local_config_dir = "configs"
        self.backup_dir = "backups"

        # Crear directorios necesarios
        for directory in [self.local_config_dir, self.backup_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)

    def get_local_config_path(self, guild_id: int) -> str:
        """Obtiene la ruta del archivo de configuración local para un servidor"""
        return os.path.join(self.local_config_dir, f"config_{guild_id}.json")

    def get_backup_path(self, timestamp: str = None) -> str:
        """Obtiene la ruta del archivo de respaldo"""
        if not timestamp:
            timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        return os.path.join(self.backup_dir, f"backup_config_{timestamp}.json")

    async def get_github_config_file(self) -> Dict[str, Any]:
        """Obtiene el archivo de configuración desde GitHub"""
        if not self.github_token:
            return {}

        url = f"https://api.github.com/repos/{self.repo_owner}/{self.repo_name}/contents/{self.config_file_path}"
        headers = {
            "Authorization": f"token {self.github_token}",
            "Accept": "application/vnd.github.v3+json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data.get("content", "")
                        sha = data.get("sha", "")

                        # Decodificar contenido base64
                        import base64
                        decoded_content = base64.b64decode(content).decode('utf-8')

                        # Extraer JSON del archivo JS
                        if "const configs = " in decoded_content:
                            json_content = decoded_content.split("const configs = ")[1].split(";")[0]
                            return json.loads(json_content)
                        else:
                            return {}
                    else:
                        print(f"Error obteniendo archivo de GitHub: {response.status}")
                        return {}
        except Exception as e:
            print(f"Error en get_github_config_file: {e}")
            return {}

    async def update_github_config_file(self, config_data: Dict[str, Any]) -> bool:
        """Actualiza el archivo de configuración en GitHub"""
        if not self.github_token:
            return False

        url = f"https://api.github.com/repos/{self.repo_owner}/{self.repo_name}/contents/{self.config_file_path}"
        headers = {
            "Authorization": f"token {self.github_token}",
            "Accept": "application/vnd.github.v3+json"
        }

        try:
            # Primero obtener el SHA actual del archivo
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        current_file_data = await response.json()
                        sha = current_file_data.get("sha", "")
                    elif response.status == 404:
                        # El archivo no existe, crear uno nuevo
                        sha = ""
                    else:
                        print(f"Error obteniendo archivo de GitHub: {response.status}")
                        return False

            # Crear contenido del archivo JS
            js_content = f"const configs = {json.dumps(config_data, indent=2, ensure_ascii=False)};"

            # Codificar en base64
            import base64
            encoded_content = base64.b64encode(js_content.encode('utf-8')).decode('utf-8')

            data = {
                "message": f"Actualización automática de configuración - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "content": encoded_content
            }

            # Solo agregar SHA si el archivo ya existe
            if sha:
                data["sha"] = sha

            # Actualizar el archivo
            async with aiohttp.ClientSession() as session:
                async with session.put(url, headers=headers, json=data) as response:
                    if response.status in [200, 201]:
                        print(f"Archivo actualizado exitosamente en GitHub")
                        return True
                    else:
                        response_text = await response.text()
                        print(f"Error actualizando archivo en GitHub: {response.status} - {response_text}")
                        return False

        except Exception as e:
            print(f"Error en update_github_config_file: {e}")
            return False

    def get_default_config(self, guild_id: int) -> Dict[str, Any]:
        """Obtiene la configuración por defecto para un servidor"""
        server_id = str(guild_id)

        return {
            "server_id": server_id,
            "server_name": "",
            "stage_channel_id": None,
            "stage_channel_name": None,
            "level_channel_id": None,
            "level_channel_name": None,
            "ticket_channel_id": None,
            "ticket_channel_name": None,
            "tts_channel_id": None,
            "tts_channel_name": None,
            "music_channel_id": None,
            "music_channel_name": None,
            "welcome_channel_id": None,
            "welcome_channel_name": None,
            "admin_roles": [],
            "bot_permissions": {
                "accept_levels": ["administrator"],
                "stage_management": ["administrator"],
                "config_management": ["administrator"],
                "moderation": ["administrator"],
                "music_management": ["administrator"]
            },
            "custom_settings": {
                "auto_dm_on_accept": True,
                "auto_dm_on_reject": True,
                "require_video_for_demons": True,
                "max_queue_size": 50,
                "enable_ticket_system": True,
                "ticket_cooldown_hours": 24,
                "enable_welcome_system": True,
                "enable_goodbye_system": True,
                "enable_auto_moderation": True,
                "enable_music_system": True,
                "enable_tts_system": True
            },
            "leveling_settings": {
                "base_xp": 5,
                "xp_cooldown_minutes": 3,
                "level_roles": {},
                "top_roles": {},
                "xp_multiplier": 1.0,
                "max_daily_xp": 1000
            },
            "anti_raid_settings": {
                "enabled": True,
                "spam_threshold": 8,
                "time_window": 10,
                "max_warnings": 3,
                "ban_duration_days": 7,
                "appeal_message": "Has sido baneado por actividad sospechosa. Puedes apelar tu baneo creando un ticket.",
                "auto_ban": True,
                "log_actions": True
            },
            "tts_settings": {
                "enabled": True,
                "language": "es",
                "speed": 1.0,
                "volume": 0.8,
                "engine": "pyttsx3",
                "auto_join": False,
                "paimon_mode": False
            },
            "music_settings": {
                "enabled": True,
                "default_volume": 0.5,
                "max_queue_size": 20,
                "auto_disconnect": True,
                "disconnect_timeout": 300,
                "loop_mode": "none"
            },
            "welcome_settings": {
                "enabled": True,
                "message": "¡Bienvenido {user} al servidor!",
                "embed_color": "#00ff00",
                "dm_welcome": False,
                "role_on_join": None
            },
            "goodbye_settings": {
                "enabled": True,
                "message": "¡Adiós {user}! Esperamos verte pronto.",
                "embed_color": "#ff0000",
                "log_leaves": True
            },
            "moderation_settings": {
                "auto_delete_invites": True,
                "auto_delete_spam": True,
                "warn_on_links": False,
                "mute_duration": 300,
                "log_mod_actions": True
            },
            "created_at": str(datetime.now()),
            "last_updated": str(datetime.now())
        }

    async def get_server_config(self, guild_id: int) -> Dict[str, Any]:
        """Obtiene la configuración de un servidor (local + GitHub)"""
        server_id = str(guild_id)
        default_config = self.get_default_config(guild_id)

        # Intentar cargar desde GitHub
        try:
            github_configs = await self.get_github_config_file()
            if server_id in github_configs:
                # Combinar configuración de GitHub con valores por defecto
                github_config = github_configs[server_id]
                for key, value in default_config.items():
                    if key not in github_config:
                        github_config[key] = value
                return github_config
        except Exception as e:
            print(f"Error cargando configuración de GitHub: {e}")

        # Si no hay GitHub o falla, usar configuración local
        local_path = self.get_local_config_path(guild_id)
        try:
            if os.path.exists(local_path):
                with open(local_path, 'r', encoding='utf-8') as f:
                    local_config = json.load(f)
                    # Combinar con valores por defecto
                    for key, value in default_config.items():
                        if key not in local_config:
                            local_config[key] = value
                    return local_config
        except Exception as e:
            print(f"Error cargando configuración local: {e}")

        return default_config

    async def update_server_config(self, guild_id: int, config_updates: Dict[str, Any]) -> bool:
        """Actualiza la configuración de un servidor (local + GitHub)"""
        server_id = str(guild_id)

        try:
            # Obtener configuración actual
            current_config = await self.get_server_config(guild_id)

            # Actualizar configuración
            current_config.update(config_updates)
            current_config["last_updated"] = str(datetime.now())

            # Guardar localmente
            local_path = self.get_local_config_path(guild_id)
            with open(local_path, 'w', encoding='utf-8') as f:
                json.dump(current_config, f, indent=4, ensure_ascii=False)

            print(f"Configuración guardada localmente para servidor {guild_id}")

            # Intentar sincronizar con GitHub
            if self.github_token:
                try:
                    print(f"Intentando sincronizar con GitHub para servidor {guild_id}...")
                    github_configs = await self.get_github_config_file()
                    github_configs[server_id] = current_config
                    github_success = await self.update_github_config_file(github_configs)
                    if github_success:
                        print(f"✅ Configuración sincronizada con GitHub para servidor {guild_id}")
                    else:
                        print(f"❌ Error sincronizando con GitHub para servidor {guild_id}")
                except Exception as e:
                    print(f"❌ Error en sincronización GitHub: {e}")

            return True

        except Exception as e:
            print(f"Error actualizando configuración: {e}")
            return False

    async def save_all_configs(self) -> Dict[str, Any]:
        """Guarda todas las configuraciones locales y las sincroniza con GitHub"""
        try:
            # Obtener todas las configuraciones locales
            all_configs = {}
            if os.path.exists(self.local_config_dir):
                for filename in os.listdir(self.local_config_dir):
                    if filename.startswith("config_") and filename.endswith(".json"):
                        guild_id = filename.replace("config_", "").replace(".json", "")
                        try:
                            local_path = os.path.join(self.local_config_dir, filename)
                            with open(local_path, 'r', encoding='utf-8') as f:
                                config = json.load(f)
                                all_configs[guild_id] = config
                        except Exception as e:
                            print(f"Error cargando configuración {filename}: {e}")

            # Crear respaldo antes de sincronizar
            backup_path = self.get_backup_path()
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(all_configs, f, indent=4, ensure_ascii=False)

            # Sincronizar con GitHub
            github_success = False
            if self.github_token and all_configs:
                try:
                    github_success = await self.update_github_config_file(all_configs)
                except Exception as e:
                    print(f"Error sincronizando con GitHub: {e}")

            return {
                "success": True,
                "local_configs": len(all_configs),
                "backup_created": backup_path,
                "github_synced": github_success,
                "timestamp": str(datetime.now())
            }

        except Exception as e:
            print(f"Error guardando todas las configuraciones: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": str(datetime.now())
            }

    async def sync_all_configs_to_github(self) -> bool:
        """Sincroniza todas las configuraciones locales con GitHub"""
        try:
            # Obtener todas las configuraciones locales
            all_configs = {}
            if os.path.exists(self.local_config_dir):
                for filename in os.listdir(self.local_config_dir):
                    if filename.startswith("config_") and filename.endswith(".json"):
                        guild_id = filename.replace("config_", "").replace(".json", "")
                        try:
                            local_path = os.path.join(self.local_config_dir, filename)
                            with open(local_path, 'r', encoding='utf-8') as f:
                                config = json.load(f)
                                all_configs[guild_id] = config
                        except Exception as e:
                            print(f"Error cargando configuración {filename}: {e}")

            if not all_configs:
                print("No hay configuraciones locales para sincronizar")
                return False

            # Sincronizar con GitHub
            return await self.update_github_config_file(all_configs)

        except Exception as e:
            print(f"Error sincronizando configuraciones: {e}")
            return False

    async def backup_configs(self) -> bool:
        """Crea un respaldo de todas las configuraciones"""
        try:
            # Obtener configuraciones de GitHub
            github_configs = await self.get_github_config_file()

            # Obtener configuraciones locales
            local_configs = {}
            if os.path.exists(self.local_config_dir):
                for filename in os.listdir(self.local_config_dir):
                    if filename.startswith("config_") and filename.endswith(".json"):
                        guild_id = filename.replace("config_", "").replace(".json", "")
                        try:
                            local_path = os.path.join(self.local_config_dir, filename)
                            with open(local_path, 'r', encoding='utf-8') as f:
                                config = json.load(f)
                                local_configs[guild_id] = config
                        except Exception as e:
                            print(f"Error cargando configuración {filename}: {e}")

            # Combinar configuraciones (local tiene prioridad)
            all_configs = {**github_configs, **local_configs}

            # Crear respaldo
            backup_path = self.get_backup_path()
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(all_configs, f, indent=4, ensure_ascii=False)

            print(f"Respaldo creado: {backup_path}")
            return True

        except Exception as e:
            print(f"Error creando respaldo: {e}")
            return False

    def get_config_summary(self, guild_id: int) -> Dict[str, Any]:
        """Obtiene un resumen de la configuración del servidor"""
        try:
            local_path = self.get_local_config_path(guild_id)
            has_local_config = os.path.exists(local_path)

            return {
                "has_local_config": has_local_config,
                "has_github_token": bool(self.github_token),
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "config_file_size": os.path.getsize(local_path) if has_local_config else 0
            }
        except Exception as e:
            return {
                "has_local_config": False,
                "has_github_token": bool(self.github_token),
                "last_updated": "Error",
                "error": str(e)
            }

    async def get_all_configs_summary(self) -> Dict[str, Any]:
        """Obtiene un resumen de todas las configuraciones"""
        try:
            # Contar configuraciones locales
            local_count = 0
            if os.path.exists(self.local_config_dir):
                local_count = len([f for f in os.listdir(self.local_config_dir) 
                                 if f.startswith("config_") and f.endswith(".json")])

            # Obtener configuraciones de GitHub
            github_configs = await self.get_github_config_file()
            github_count = len(github_configs)

            # Contar respaldos
            backup_count = 0
            if os.path.exists(self.backup_dir):
                backup_count = len([f for f in os.listdir(self.backup_dir) 
                                  if f.startswith("backup_config_") and f.endswith(".json")])

            return {
                "local_configs": local_count,
                "github_configs": github_count,
                "backups": backup_count,
                "github_connected": bool(self.github_token),
                "last_sync": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            return {
                "error": str(e),
                "local_configs": 0,
                "github_configs": 0,
                "backups": 0,
                "github_connected": False
            }

# Variables globales para el manager
_config_manager = None

def init_config_manager(github_token: str = None):
    """Inicializa el manager de configuración"""
    global _config_manager
    _config_manager = BotConfigManager(github_token)
    return _config_manager

def get_config_manager() -> BotConfigManager:
    """Obtiene la instancia del manager de configuración"""
    global _config_manager
    if _config_manager is None:
        _config_manager = BotConfigManager()
    return _config_manager
