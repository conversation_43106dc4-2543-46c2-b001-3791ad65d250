# 🎤 APIs de TTS para Paimon Bot

Este documento explica cómo configurar y usar las diferentes APIs de Text-to-Speech disponibles en el bot.

## 🌐 APIs Disponibles

### 1. **Google Cloud TTS** (Recomendado)
- **Calidad:** Excelente
- **Costo:** Gratis hasta 4 millones de caracteres/mes
- **Voces:** Múltiples idiomas y voces neurales

#### Configuración:
1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la API "Cloud Text-to-Speech"
4. <PERSON>rea credenciales (API Key)
5. Usa el comando: `!ttsapi google_cloud set YOUR_API_KEY`

### 2. **ElevenLabs**
- **Calidad:** Muy alta (voces realistas)
- **Costo:** Grat<PERSON> hasta 10,000 caracteres/mes
- **Voces:** Voces clonadas y personalizadas

#### Configuración:
1. Regístrate en [ElevenLabs](https://elevenlabs.io/)
2. Ve a tu perfil y copia tu API Key
3. Usa el comando: `!ttsapi elevenlabs set YOUR_API_KEY`

### 3. **Azure Cognitive Services**
- **Calidad:** Excelente
- **Costo:** $16 por millón de caracteres
- **Voces:** Múltiples idiomas y estilos

#### Configuración:
1. Ve a [Azure Portal](https://portal.azure.com/)
2. Crea un recurso "Speech Services"
3. Copia la subscription key y región
4. Usa los comandos:
   - `!ttsapi azure set YOUR_SUBSCRIPTION_KEY`
   - Configura la región en variables de entorno

## 🔧 Comandos de Configuración

### Listar APIs disponibles:
```
!ttsapi list
```

### Configurar API Key:
```
!ttsapi <api_name> set <api_key>
```

### Probar API:
```
!ttsapi <api_name> test
```

### Cambiar motor TTS:
```
!ttsconfig engine <motor>
```

## 📋 Ejemplos de Uso

### Configurar Google Cloud TTS:
```
!ttsapi google_cloud set AIzaSyC...
!ttsconfig engine google_cloud
!ttsapi google_cloud test
```

### Configurar ElevenLabs:
```
!ttsapi elevenlabs set 1234567890abcdef...
!ttsconfig engine elevenlabs
!ttsapi elevenlabs test
```

### Configurar Azure:
```
!ttsapi azure set 1234567890abcdef...
!ttsconfig engine azure
!ttsapi azure test
```

## 🌍 Idiomas Soportados

### Google Cloud TTS:
- Español (es-ES)
- Inglés (en-US)
- Francés (fr-FR)
- Alemán (de-DE)
- Y muchos más...

### ElevenLabs:
- Español
- Inglés
- Francés
- Alemán
- Italiano
- Portugués
- Japonés
- Coreano
- Chino

### Azure:
- Español (es-ES)
- Inglés (en-US)
- Francés (fr-FR)
- Alemán (de-DE)
- Y muchos más...

## ⚙️ Configuración Avanzada

### Variables de Entorno:
```bash
# Google Cloud
GOOGLE_CLOUD_API_KEY=your_api_key

# ElevenLabs
ELEVENLABS_API_KEY=your_api_key

# Azure
AZURE_SPEECH_KEY=your_subscription_key
AZURE_SPEECH_REGION=eastus
```

### Voces Específicas:

#### Google Cloud:
- `es-ES-Neural2-A` (Femenina)
- `es-ES-Neural2-B` (Masculina)
- `es-ES-Neural2-C` (Femenina)
- `es-ES-Neural2-D` (Masculina)

#### ElevenLabs:
- `21m00Tcm4TlvDq8ikWAM` (Rachel - Inglés)
- `AZnzlk1XvdvUeBnXmlld` (Domi - Inglés)
- `EXAVITQu4vr4xnSDxMaL` (Bella - Inglés)

#### Azure:
- `es-ES-ElviraNeural` (Femenina)
- `es-ES-AlvaroNeural` (Masculina)
- `es-ES-AbrilNeural` (Femenina)

## 🎯 Recomendaciones

### Para Uso Gratuito:
1. **Google Cloud TTS** - Mejor opción gratuita
2. **ElevenLabs** - Para voces más realistas

### Para Uso Profesional:
1. **Azure** - Mejor integración empresarial
2. **Google Cloud** - Mejor calidad/precio

### Para Desarrollo:
1. **pyttsx3** - Sin límites, offline
2. **gTTS** - Gratis, online

## 🔍 Solución de Problemas

### Error "API key not found":
- Verifica que configuraste la API key correctamente
- Usa `!ttsapi <api> test` para verificar

### Error "API not working":
- Verifica tu conexión a internet
- Revisa que la API key sea válida
- Consulta la documentación de la API

### Error "Rate limit exceeded":
- Espera un momento antes de usar más TTS
- Considera cambiar a una API con más límites

## 📞 Soporte

Si tienes problemas con las APIs:
1. Verifica la configuración con `!ttsapi list`
2. Prueba la API con `!ttsapi <api> test`
3. Revisa los logs del bot para errores específicos
4. Consulta la documentación oficial de cada API 