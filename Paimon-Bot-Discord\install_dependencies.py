#!/usr/bin/env python3
"""
Script de instalación automática para Paimon Bot
Instala todas las dependencias necesarias incluyendo cuentas encriptadas
"""

import subprocess
import sys
import os
import platform

def run_command(command, description=""):
    """Ejecuta un comando y maneja errores"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Completado")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Error: {e}")
        print(f"   Salida: {e.stdout}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Verifica la versión de Python"""
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Se requiere Python 3.8 o superior")
        return False
    
    print("✅ Versión de Python compatible")
    return True

def install_system_dependencies():
    """Instala dependencias del sistema según el OS"""
    system = platform.system().lower()
    
    if system == "linux":
        print("🐧 Detectado: Linux")
        commands = [
            ("sudo apt update", "Actualizando repositorios"),
            ("sudo apt install -y build-essential libffi-dev python3-dev", "Instalando herramientas de compilación"),
            ("sudo apt install -y ffmpeg", "Instalando FFmpeg")
        ]
        
        for cmd, desc in commands:
            if not run_command(cmd, desc):
                print("⚠️ Algunos paquetes del sistema fallaron, continuando...")
    
    elif system == "darwin":  # macOS
        print("🍎 Detectado: macOS")
        run_command("xcode-select --install", "Instalando Xcode Command Line Tools")
        run_command("brew install ffmpeg", "Instalando FFmpeg con Homebrew")
    
    elif system == "windows":
        print("🪟 Detectado: Windows")
        print("💡 Asegúrate de tener Visual Studio Build Tools instalado")
        print("💡 FFmpeg se instalará automáticamente con static-ffmpeg")

def install_core_dependencies():
    """Instala dependencias principales"""
    core_packages = [
        "pip>=23.0.0",
        "setuptools>=68.0.0",
        "wheel>=0.41.0"
    ]
    
    for package in core_packages:
        run_command(f"pip install --upgrade {package}", f"Instalando {package}")

def install_crypto_dependencies():
    """Instala dependencias de criptografía"""
    print("\n🔐 INSTALANDO DEPENDENCIAS DE CRIPTOGRAFÍA")
    
    crypto_packages = [
        "cryptography>=41.0.0",
        "bcrypt>=4.0.0",
        "passlib[bcrypt,argon2]>=1.7.4",
        "argon2-cffi>=23.0.0",
        "pycryptodome>=3.19.0",
        "PyJWT>=2.8.0",
        "python-jose[cryptography]>=3.3.0",
        "itsdangerous>=2.1.0"
    ]
    
    for package in crypto_packages:
        run_command(f"pip install {package}", f"Instalando {package}")

def install_database_dependencies():
    """Instala dependencias de base de datos"""
    print("\n💾 INSTALANDO DEPENDENCIAS DE BASE DE DATOS")
    
    db_packages = [
        "sqlalchemy>=2.0.0",
        "aiosqlite>=0.19.0"
    ]
    
    for package in db_packages:
        run_command(f"pip install {package}", f"Instalando {package}")

def install_bot_dependencies():
    """Instala dependencias del bot"""
    print("\n🤖 INSTALANDO DEPENDENCIAS DEL BOT")
    
    bot_packages = [
        "discord.py>=2.3.2",
        "aiohttp>=3.9.0",
        "python-dotenv>=1.0.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0"
    ]
    
    for package in bot_packages:
        run_command(f"pip install {package}", f"Instalando {package}")

def install_optional_dependencies():
    """Instala dependencias opcionales"""
    print("\n🎯 INSTALANDO DEPENDENCIAS OPCIONALES")
    
    optional_packages = [
        "google-generativeai>=0.3.0",
        "gd.py>=1.0.1",
        "Pillow>=10.0.0",
        "yt-dlp>=2023.12.30",
        "PyNaCl>=1.5.0",
        "psutil>=5.9.0",
        "flask>=2.3.0"
    ]
    
    for package in optional_packages:
        success = run_command(f"pip install {package}", f"Instalando {package}")
        if not success:
            print(f"⚠️ {package} falló, continuando...")

def install_from_requirements():
    """Instala desde requirements.txt si existe"""
    if os.path.exists("requirements.txt"):
        print("\n📋 INSTALANDO DESDE REQUIREMENTS.TXT")
        run_command("pip install -r requirements.txt", "Instalando desde requirements.txt")
    else:
        print("⚠️ requirements.txt no encontrado")

def verify_installation():
    """Verifica que las dependencias críticas estén instaladas"""
    print("\n🔍 VERIFICANDO INSTALACIÓN")
    
    critical_imports = [
        ("discord", "Discord.py"),
        ("cryptography", "Cryptography"),
        ("bcrypt", "Bcrypt"),
        ("passlib", "Passlib"),
        ("jwt", "PyJWT"),
        ("sqlalchemy", "SQLAlchemy"),
        ("aiosqlite", "AIOSQLite")
    ]
    
    failed_imports = []
    
    for module, name in critical_imports:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
        except ImportError:
            print(f"❌ {name} - FALLO")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n⚠️ Módulos que fallaron: {', '.join(failed_imports)}")
        print("💡 Intenta instalarlos manualmente:")
        for name in failed_imports:
            print(f"   pip install {name.lower()}")
        return False
    
    print("\n🎉 ¡Todas las dependencias críticas están instaladas!")
    return True

def main():
    """Función principal"""
    print("=" * 60)
    print("🤖 PAIMON BOT - INSTALADOR DE DEPENDENCIAS")
    print("=" * 60)
    
    # Verificar Python
    if not check_python_version():
        sys.exit(1)
    
    # Instalar dependencias del sistema
    install_system_dependencies()
    
    # Actualizar pip y herramientas
    install_core_dependencies()
    
    # Instalar dependencias por categorías
    install_crypto_dependencies()
    install_database_dependencies()
    install_bot_dependencies()
    install_optional_dependencies()
    
    # Instalar desde requirements.txt
    install_from_requirements()
    
    # Verificar instalación
    if verify_installation():
        print("\n🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!")
        print("\n💡 Próximos pasos:")
        print("   1. Configura tus variables de entorno (.env)")
        print("   2. Ejecuta el bot: python main.py")
        print("   3. Prueba las funciones de cuentas encriptadas")
    else:
        print("\n⚠️ Instalación completada con algunos errores")
        print("💡 Revisa los errores arriba e instala manualmente los paquetes faltantes")

if __name__ == "__main__":
    main()
