#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 Sistema de Autenticación Seguro - Paimon Bot
Implementa OAuth2 de Discord y elimina almacenamiento de contraseñas
"""

import os
import jwt
import time
import secrets
import hashlib
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
import discord
from discord.ext import commands

class SecureAuthManager:
    """Gestor de autenticación seguro usando OAuth2 de Discord"""
    
    def __init__(self, bot: discord.Bot):
        self.bot = bot
        self.logger = logging.getLogger('SecureAuth')
        
        # Configuración OAuth2
        self.client_id = os.getenv('DISCORD_CLIENT_ID')
        self.client_secret = os.getenv('DISCORD_CLIENT_SECRET')
        self.redirect_uri = os.getenv('DISCORD_REDIRECT_URI', 'http://localhost:8080/callback')
        
        # JWT Secret para tokens seguros
        self.jwt_secret = os.getenv('JWT_SECRET', secrets.token_urlsafe(32))
        
        # Almacenamiento temporal de sesiones (en memoria, no persistente)
        self.active_sessions = {}
        self.pending_auth = {}
        
        # Configuración de seguridad
        self.session_timeout = 3600  # 1 hora
        self.max_sessions_per_user = 3
        
        self.logger.info("🔐 SecureAuthManager inicializado")
    
    def generate_oauth_url(self, user_id: str, guild_id: str) -> str:
        """Genera URL de OAuth2 para autenticación con Discord"""
        try:
            # Crear estado único para prevenir CSRF
            state = self.create_secure_state(user_id, guild_id)
            
            # Scopes necesarios para el bot
            scopes = ['identify', 'guilds']
            scope_string = '%20'.join(scopes)
            
            oauth_url = (
                f"https://discord.com/api/oauth2/authorize?"
                f"client_id={self.client_id}&"
                f"redirect_uri={self.redirect_uri}&"
                f"response_type=code&"
                f"scope={scope_string}&"
                f"state={state}"
            )
            
            # Guardar estado pendiente
            self.pending_auth[state] = {
                'user_id': user_id,
                'guild_id': guild_id,
                'timestamp': time.time(),
                'expires_at': time.time() + 300  # 5 minutos
            }
            
            return oauth_url
            
        except Exception as e:
            self.logger.error(f"❌ Error generando OAuth URL: {e}")
            return None
    
    def create_secure_state(self, user_id: str, guild_id: str) -> str:
        """Crea un estado seguro para OAuth2"""
        timestamp = str(int(time.time()))
        data = f"{user_id}:{guild_id}:{timestamp}"
        
        # Crear hash seguro
        state_hash = hashlib.sha256(
            f"{data}:{self.jwt_secret}".encode()
        ).hexdigest()[:16]
        
        return f"{state_hash}_{timestamp}"
    
    def verify_state(self, state: str) -> Optional[Dict[str, Any]]:
        """Verifica y decodifica el estado OAuth2"""
        try:
            if state not in self.pending_auth:
                return None
            
            auth_data = self.pending_auth[state]
            
            # Verificar expiración
            if time.time() > auth_data['expires_at']:
                del self.pending_auth[state]
                return None
            
            return auth_data
            
        except Exception as e:
            self.logger.error(f"❌ Error verificando estado: {e}")
            return None
    
    async def process_oauth_callback(self, code: str, state: str) -> Optional[Dict[str, Any]]:
        """Procesa el callback de OAuth2 y crea sesión segura"""
        try:
            # Verificar estado
            auth_data = self.verify_state(state)
            if not auth_data:
                self.logger.warning("⚠️ Estado OAuth2 inválido o expirado")
                return None
            
            # Intercambiar código por token de acceso
            token_data = await self.exchange_code_for_token(code)
            if not token_data:
                return None
            
            # Obtener información del usuario
            user_info = await self.get_user_info(token_data['access_token'])
            if not user_info:
                return None
            
            # Verificar que el usuario coincide
            if str(user_info['id']) != auth_data['user_id']:
                self.logger.warning("⚠️ ID de usuario no coincide en OAuth2")
                return None
            
            # Crear sesión segura
            session_token = self.create_secure_session(
                user_info['id'],
                auth_data['guild_id'],
                user_info
            )
            
            # Limpiar estado pendiente
            del self.pending_auth[state]
            
            return {
                'session_token': session_token,
                'user_info': user_info,
                'expires_at': time.time() + self.session_timeout
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error procesando OAuth callback: {e}")
            return None
    
    async def exchange_code_for_token(self, code: str) -> Optional[Dict[str, Any]]:
        """Intercambia código OAuth2 por token de acceso"""
        try:
            import aiohttp
            
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'authorization_code',
                'code': code,
                'redirect_uri': self.redirect_uri
            }
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://discord.com/api/oauth2/token',
                    data=data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"❌ Error intercambiando código: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"❌ Error en intercambio de token: {e}")
            return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Obtiene información del usuario usando el token de acceso"""
        try:
            import aiohttp
            
            headers = {
                'Authorization': f'Bearer {access_token}'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://discord.com/api/users/@me',
                    headers=headers
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"❌ Error obteniendo info de usuario: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo info de usuario: {e}")
            return None
    
    def create_secure_session(self, user_id: str, guild_id: str, user_info: Dict[str, Any]) -> str:
        """Crea un token de sesión seguro usando JWT"""
        try:
            # Limpiar sesiones expiradas del usuario
            self.cleanup_user_sessions(user_id)
            
            # Verificar límite de sesiones
            user_sessions = [
                s for s in self.active_sessions.values() 
                if s.get('user_id') == user_id
            ]
            
            if len(user_sessions) >= self.max_sessions_per_user:
                # Eliminar la sesión más antigua
                oldest_session = min(user_sessions, key=lambda s: s['created_at'])
                self.revoke_session(oldest_session['token'])
            
            # Crear payload del JWT
            payload = {
                'user_id': user_id,
                'guild_id': guild_id,
                'username': user_info.get('username', 'Unknown'),
                'discriminator': user_info.get('discriminator', '0000'),
                'created_at': time.time(),
                'expires_at': time.time() + self.session_timeout,
                'session_id': secrets.token_urlsafe(16)
            }
            
            # Crear JWT
            token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
            
            # Guardar sesión activa
            self.active_sessions[token] = payload
            
            self.logger.info(f"✅ Sesión creada para usuario {user_id}")
            return token
            
        except Exception as e:
            self.logger.error(f"❌ Error creando sesión: {e}")
            return None
    
    def verify_session(self, token: str) -> Optional[Dict[str, Any]]:
        """Verifica un token de sesión"""
        try:
            if not token or token not in self.active_sessions:
                return None
            
            # Decodificar JWT
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            
            # Verificar expiración
            if time.time() > payload['expires_at']:
                self.revoke_session(token)
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            self.revoke_session(token)
            return None
        except jwt.InvalidTokenError:
            return None
        except Exception as e:
            self.logger.error(f"❌ Error verificando sesión: {e}")
            return None
    
    def revoke_session(self, token: str) -> bool:
        """Revoca una sesión específica"""
        try:
            if token in self.active_sessions:
                del self.active_sessions[token]
                self.logger.info("✅ Sesión revocada")
                return True
            return False
        except Exception as e:
            self.logger.error(f"❌ Error revocando sesión: {e}")
            return False
    
    def cleanup_user_sessions(self, user_id: str):
        """Limpia sesiones expiradas de un usuario"""
        try:
            current_time = time.time()
            expired_tokens = [
                token for token, session in self.active_sessions.items()
                if session.get('user_id') == user_id and current_time > session.get('expires_at', 0)
            ]
            
            for token in expired_tokens:
                del self.active_sessions[token]
                
        except Exception as e:
            self.logger.error(f"❌ Error limpiando sesiones: {e}")
    
    def get_user_sessions(self, user_id: str) -> list:
        """Obtiene todas las sesiones activas de un usuario"""
        try:
            return [
                {
                    'session_id': session['session_id'],
                    'created_at': session['created_at'],
                    'expires_at': session['expires_at'],
                    'guild_id': session['guild_id']
                }
                for session in self.active_sessions.values()
                if session.get('user_id') == user_id
            ]
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo sesiones: {e}")
            return []
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas del sistema de autenticación"""
        try:
            current_time = time.time()
            active_sessions = len(self.active_sessions)
            pending_auths = len(self.pending_auth)
            
            # Limpiar pendientes expirados
            expired_pending = [
                state for state, data in self.pending_auth.items()
                if current_time > data['expires_at']
            ]
            
            for state in expired_pending:
                del self.pending_auth[state]
            
            return {
                'active_sessions': active_sessions,
                'pending_authentications': len(self.pending_auth),
                'oauth_configured': bool(self.client_id and self.client_secret),
                'session_timeout': self.session_timeout,
                'max_sessions_per_user': self.max_sessions_per_user
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error obteniendo estadísticas: {e}")
            return {}

# Función de utilidad para crear el manager
def create_auth_manager(bot: discord.Bot) -> SecureAuthManager:
    """Crea una instancia del SecureAuthManager"""
    return SecureAuthManager(bot)
