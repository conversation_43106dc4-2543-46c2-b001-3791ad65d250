# ==========================================
# PAIMON BOT - CONFIGURACIÓN DE VARIABLES DE ENTORNO
# ==========================================
# Copia este archivo como .env y completa con tus valores reales

# ==========================================
# TOKENS PRINCIPALES
# ==========================================

# Token del bot de Discord (OBLIGATORIO)
TOKEN=tu-token-de-discord-aqui

# Token de GitHub para sincronización (OPCIONAL)
GITHUB_TOKEN=ghp_tu-token-de-github-aqui

# API Key de Google Gemini para IA (OPCIONAL)
GEMINI_API_KEY=tu-clave-de-gemini-aqui

# API Key de OpenAI (OPCIONAL)
OPENAI_API_KEY=sk-tu-clave-de-openai-aqui

# ==========================================
# CRIPTOGRAFÍA Y SEGURIDAD (Cuentas Encriptadas)
# ==========================================

# Clave secreta principal (OBLIGATORIO para cuentas encriptadas)
SECRET_KEY=tu-clave-secreta-muy-larga-y-compleja-de-al-menos-32-caracteres

# Clave para encriptación de datos (OBLIGATORIO para cuentas encriptadas)
ENCRYPTION_KEY=otra-clave-para-encriptacion-de-al-menos-32-caracteres

# Clave para tokens JWT (OBLIGATORIO para cuentas encriptadas)
JWT_SECRET=clave-para-tokens-jwt-de-al-menos-32-caracteres

# Clave para encriptación de base de datos (OBLIGATORIO para cuentas encriptadas)
DATABASE_ENCRYPTION_KEY=clave-para-base-de-datos-de-al-menos-32-caracteres

# Salt para hashing de contraseñas (OBLIGATORIO para cuentas encriptadas)
PASSWORD_SALT=salt-para-contraseñas-unico-y-aleatorio

# ==========================================
# CONFIGURACIÓN DEL BOT
# ==========================================

# Versión del bot
BOT_VERSION=2.1.95

# Prefijo de comandos
COMMAND_PREFIX=!

# ID del servidor exclusivo del owner (OPCIONAL)
OWNER_SERVER_ID=tu-id-de-servidor-aqui

# ID del owner del bot (OPCIONAL)
OWNER_ID=tu-id-de-usuario-aqui

# ==========================================
# BASE DE DATOS
# ==========================================

# URL de la base de datos (por defecto SQLite local)
DATABASE_URL=sqlite:///data/paimon_bot.db

# Configuración de backup automático
AUTO_BACKUP=true
BACKUP_INTERVAL=3600  # En segundos (1 hora)

# ==========================================
# APIS EXTERNAS
# ==========================================

# YouTube API Key (OPCIONAL para música)
YOUTUBE_API_KEY=tu-clave-de-youtube-aqui

# Spotify Client ID y Secret (OPCIONAL para música)
SPOTIFY_CLIENT_ID=tu-client-id-de-spotify
SPOTIFY_CLIENT_SECRET=tu-client-secret-de-spotify

# ==========================================
# CONFIGURACIÓN DE HOSTING
# ==========================================

# Puerto para keep-alive web server
PORT=8080

# Host para el servidor web
HOST=0.0.0.0

# Modo de desarrollo (true/false)
DEBUG=false

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ==========================================
# CONFIGURACIÓN DE CACHE
# ==========================================

# Tiempo de cache en segundos
CACHE_TTL=3600

# Tamaño máximo de cache en MB
MAX_CACHE_SIZE=100

# ==========================================
# CONFIGURACIÓN DE SEGURIDAD
# ==========================================

# Tiempo de expiración de tokens en segundos
TOKEN_EXPIRY=86400  # 24 horas

# Máximo de intentos de login
MAX_LOGIN_ATTEMPTS=5

# Tiempo de bloqueo después de intentos fallidos (segundos)
LOCKOUT_TIME=1800  # 30 minutos

# Habilitar autenticación de dos factores
ENABLE_2FA=true

# ==========================================
# CONFIGURACIÓN DE NOTIFICACIONES
# ==========================================

# Canal de logs del bot (ID del canal)
LOG_CHANNEL_ID=tu-id-de-canal-de-logs

# Canal de errores críticos (ID del canal)
ERROR_CHANNEL_ID=tu-id-de-canal-de-errores

# Webhook para notificaciones externas (OPCIONAL)
WEBHOOK_URL=tu-webhook-url-aqui

# ==========================================
# CONFIGURACIÓN AVANZADA
# ==========================================

# Límite de rate limiting (requests por minuto)
RATE_LIMIT=60

# Timeout para requests HTTP (segundos)
HTTP_TIMEOUT=30

# Máximo de conexiones concurrentes
MAX_CONNECTIONS=100

# Habilitar métricas de rendimiento
ENABLE_METRICS=true

# ==========================================
# CONFIGURACIÓN DE DESARROLLO
# ==========================================

# Habilitar modo de testing
TESTING_MODE=false

# Cargar comandos de testing
LOAD_TEST_COMMANDS=false

# Habilitar hot reload
HOT_RELOAD=false

# ==========================================
# NOTAS IMPORTANTES
# ==========================================

# 1. NUNCA compartas tu archivo .env
# 2. Usa claves largas y aleatorias para seguridad
# 3. Cambia las claves regularmente
# 4. Mantén backups seguros de tus claves
# 5. Para generar claves seguras, usa:
#    python -c "import secrets; print(secrets.token_urlsafe(32))"

# ==========================================
# EJEMPLO DE GENERACIÓN DE CLAVES SEGURAS
# ==========================================

# Para generar claves aleatorias seguras, ejecuta en Python:
# import secrets
# print("SECRET_KEY=" + secrets.token_urlsafe(32))
# print("ENCRYPTION_KEY=" + secrets.token_urlsafe(32))
# print("JWT_SECRET=" + secrets.token_urlsafe(32))
# print("DATABASE_ENCRYPTION_KEY=" + secrets.token_urlsafe(32))
# print("PASSWORD_SALT=" + secrets.token_urlsafe(16))
