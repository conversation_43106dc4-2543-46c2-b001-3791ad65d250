#!/usr/bin/env python3
"""
Instalador del Sistema de Cifrado para Replit - Paimon Bot
Instala y configura el sistema de cifrado específicamente para Replit
"""

import os
import sys
import subprocess
import importlib

def run_command(command):
    """Ejecutar comando y capturar salida"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_with_pip(package):
    """Instalar paquete usando pip"""
    print(f"⏳ Instalando {package}...")
    success, stdout, stderr = run_command(f"pip install {package}")
    if success:
        print(f"✅ {package} instalado exitosamente")
        return True
    else:
        print(f"❌ Error instalando {package}: {stderr}")
        return False

def install_with_poetry(package):
    """Instalar paquete usando poetry"""
    print(f"⏳ Instalando {package} con Poetry...")
    success, stdout, stderr = run_command(f"poetry add {package}")
    if success:
        print(f"✅ {package} instalado exitosamente con Poetry")
        return True
    else:
        print(f"❌ Error instalando {package} con Poetry: {stderr}")
        return False

def check_package(package_name, import_name=None):
    """Verificar si un paquete está instalado"""
    if import_name is None:
        import_name = package_name

    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def detect_replit():
    """Detectar si estamos ejecutando en Replit"""
    replit_indicators = [
        os.getenv('REPL_ID'),
        os.getenv('REPLIT_DB_URL'),
        os.getenv('REPL_SLUG'),
        os.path.exists('/home/<USER>')
    ]
    return any(replit_indicators)

def main():
    print("🔐 Instalador del Sistema de Cifrado para Replit - Paimon Bot")
    print("=" * 70)

    # Detectar entorno
    is_replit = detect_replit()
    if is_replit:
        print("🌐 Entorno detectado: Replit")
    else:
        print("🖥️ Entorno detectado: Local/Otro")

    # Lista de dependencias requeridas
    dependencies = [
        ("cryptography", "cryptography"),
        ("pycryptodome", "Crypto"),
        ("flask", "flask")
    ]

    print("\n📋 Verificando dependencias...")

    missing_packages = []
    for package, import_name in dependencies:
        if check_package(package, import_name):
            print(f"✅ {package} - Instalado")
        else:
            print(f"❌ {package} - No encontrado")
            missing_packages.append(package)

    if not missing_packages:
        print("\n🎉 ¡Todas las dependencias están instaladas!")
        setup_directories()
        print("✅ El sistema de cifrado está listo para usar en Replit.")
        return

    print(f"\n📦 Instalando {len(missing_packages)} paquetes faltantes...")

    # Intentar instalar con diferentes métodos
    failed_packages = []

    for package in missing_packages:
        success = False

        # Método 1: Poetry (preferido en Replit)
        if is_replit:
            success = install_with_poetry(package)

        # Método 2: pip como respaldo
        if not success:
            success = install_with_pip(package)

        if not success:
            failed_packages.append(package)

    print("\n" + "=" * 70)

    if not failed_packages:
        print("🎉 ¡Instalación completada exitosamente!")
        setup_directories()
        show_replit_instructions()
    else:
        print("⚠️ Algunos paquetes no se pudieron instalar:")
        for package in failed_packages:
            print(f"❌ {package}")

        print("\n🔧 Soluciones para Replit:")
        print("1. Actualiza pyproject.toml con las dependencias")
        print("2. Ejecuta 'poetry install' en la consola")
        print("3. Reinicia el Repl")

        show_manual_setup()

def setup_directories():
    """Crear directorios necesarios"""
    directories = ["data", "src"]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Directorio {directory}/ creado")
        else:
            print(f"📁 Directorio {directory}/ ya existe")

def show_replit_instructions():
    """Mostrar instrucciones específicas para Replit"""
    print("\n🌐 Configuración específica para Replit:")
    print("=" * 50)

    print("1. 📁 Archivos creados:")
    print("   • data/ - Archivos .Paimon cifrados")
    print("   • src/ - Código del sistema de cifrado")

    print("\n2. 🔧 Variables de entorno recomendadas:")
    print("   • GITHUB_TOKEN - Para backup automático")
    print("   • GITHUB_REPO_OWNER - Tu usuario de GitHub")
    print("   • GITHUB_REPO_NAME - Nombre del repositorio")

    print("\n3. 🚀 Comandos disponibles:")
    print("   • !register <usuario> <contraseña> - Registrar cuenta")
    print("   • !login <contraseña> - Iniciar sesión")
    print("   • !account - Ver estado de cuenta")
    print("   • !logout - Cerrar sesión")
    print("   • !cryptostats - Estadísticas (admin)")

    print("\n4. 🔒 Características de seguridad:")
    print("   • Cifrado AES-256-GCM")
    print("   • PBKDF2-SHA256 con 100,000 iteraciones")
    print("   • Registro y login por DM privado")
    print("   • Sistema global entre servidores")

    print("\n5. 💡 Uso en Replit:")
    print("   • El bot mantendrá los archivos cifrados")
    print("   • Usa !cryptobackup para respaldar en GitHub")
    print("   • Los datos persisten entre reinicios")

def show_manual_setup():
    """Mostrar instrucciones de configuración manual"""
    print("\n🔧 Configuración manual para Replit:")
    print("=" * 50)

    print("1. Actualiza pyproject.toml:")
    print('   dependencies = [')
    print('       "cryptography>=41.0.0",')
    print('       "pycryptodome>=3.19.0",')
    print('       "flask>=3.1.1",')
    print('       # ... otras dependencias')
    print('   ]')

    print("\n2. En la consola de Replit:")
    print("   poetry install")
    print("   poetry add cryptography pycryptodome")

    print("\n3. Reinicia el Repl:")
    print("   Presiona el botón 'Run' nuevamente")

    print("\n4. Verifica la instalación:")
    print("   python -c 'import cryptography; print(\"✅ Cifrado disponible\")'")

if __name__ == "__main__":
    main()
