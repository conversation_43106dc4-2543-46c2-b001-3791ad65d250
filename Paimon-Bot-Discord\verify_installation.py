#!/usr/bin/env python3
"""
Script de verificación para Paimon Bot
Verifica que todas las dependencias estén instaladas correctamente
"""

import sys
import os
import importlib
from datetime import datetime

def print_header():
    """Imprime el header del script"""
    print("=" * 70)
    print("🔍 PAIMON BOT - VERIFICADOR DE INSTALACIÓN")
    print("=" * 70)
    print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 70)

def check_import(module_name, package_name=None, description=""):
    """Verifica si un módulo se puede importar"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description or package_name or module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description or package_name or module_name} - Error: {e}")
        return False

def check_core_dependencies():
    """Verifica dependencias principales del bot"""
    print("\n🤖 DEPENDENCIAS PRINCIPALES DEL BOT")
    print("-" * 50)
    
    core_deps = [
        ("discord", "discord.py", "Framework principal de Discord"),
        ("aiohttp", "aiohttp", "Cliente HTTP asíncrono"),
        ("dotenv", "python-dotenv", "Manejo de variables de entorno"),
        ("requests", "requests", "Cliente HTTP"),
        ("asyncio", None, "Programación asíncrona")
    ]
    
    results = []
    for module, package, desc in core_deps:
        results.append(check_import(module, package, desc))
    
    return all(results)

def check_crypto_dependencies():
    """Verifica dependencias de criptografía"""
    print("\n🔐 DEPENDENCIAS DE CRIPTOGRAFÍA (Cuentas Encriptadas)")
    print("-" * 50)
    
    crypto_deps = [
        ("cryptography", "cryptography", "Criptografía principal"),
        ("bcrypt", "bcrypt", "Hashing de contraseñas"),
        ("passlib", "passlib", "Utilidades de contraseñas"),
        ("argon2", "argon2-cffi", "Hashing Argon2"),
        ("Crypto", "pycryptodome", "Criptografía avanzada"),
        ("jwt", "PyJWT", "Tokens JWT"),
        ("jose", "python-jose", "Tokens JOSE"),
        ("itsdangerous", "itsdangerous", "Serialización segura")
    ]
    
    results = []
    for module, package, desc in crypto_deps:
        results.append(check_import(module, package, desc))
    
    return all(results)

def check_database_dependencies():
    """Verifica dependencias de base de datos"""
    print("\n💾 DEPENDENCIAS DE BASE DE DATOS")
    print("-" * 50)
    
    db_deps = [
        ("sqlalchemy", "sqlalchemy", "ORM de base de datos"),
        ("aiosqlite", "aiosqlite", "SQLite asíncrono"),
        ("sqlite3", None, "SQLite (incluido en Python)")
    ]
    
    results = []
    for module, package, desc in db_deps:
        results.append(check_import(module, package, desc))
    
    return all(results)

def check_optional_dependencies():
    """Verifica dependencias opcionales"""
    print("\n🎯 DEPENDENCIAS OPCIONALES")
    print("-" * 50)
    
    optional_deps = [
        ("google.generativeai", "google-generativeai", "IA de Google Gemini"),
        ("gd", "gd.py", "API de Geometry Dash"),
        ("PIL", "Pillow", "Procesamiento de imágenes"),
        ("cv2", "opencv-python", "Visión por computadora"),
        ("numpy", "numpy", "Computación numérica"),
        ("yt_dlp", "yt-dlp", "Descarga de videos"),
        ("nacl", "PyNaCl", "Criptografía de audio"),
        ("pydub", "pydub", "Manipulación de audio"),
        ("psutil", "psutil", "Información del sistema"),
        ("flask", "flask", "Servidor web"),
        ("bs4", "beautifulsoup4", "Parsing HTML"),
        ("yaml", "pyyaml", "Parsing YAML"),
        ("github", "PyGithub", "API de GitHub"),
        ("git", "GitPython", "Control de versiones")
    ]
    
    success_count = 0
    for module, package, desc in optional_deps:
        if check_import(module, package, desc):
            success_count += 1
    
    print(f"\n📊 Dependencias opcionales: {success_count}/{len(optional_deps)} instaladas")
    return success_count

def check_environment_variables():
    """Verifica variables de entorno importantes"""
    print("\n🔧 VARIABLES DE ENTORNO")
    print("-" * 50)
    
    env_vars = [
        ("TOKEN", "Token de Discord", True),
        ("GEMINI_API_KEY", "API Key de Gemini", False),
        ("GITHUB_TOKEN", "Token de GitHub", False),
        ("SECRET_KEY", "Clave secreta principal", True),
        ("ENCRYPTION_KEY", "Clave de encriptación", True),
        ("JWT_SECRET", "Clave JWT", True),
        ("DATABASE_ENCRYPTION_KEY", "Clave de BD", True)
    ]
    
    missing_required = []
    missing_optional = []
    
    for var, desc, required in env_vars:
        value = os.getenv(var)
        if value:
            # Mostrar solo los primeros y últimos caracteres por seguridad
            if len(value) > 8:
                display_value = f"{value[:4]}...{value[-4:]}"
            else:
                display_value = "***"
            print(f"✅ {desc}: {display_value}")
        else:
            print(f"❌ {desc}: No configurada")
            if required:
                missing_required.append(var)
            else:
                missing_optional.append(var)
    
    return missing_required, missing_optional

def check_file_structure():
    """Verifica la estructura de archivos"""
    print("\n📁 ESTRUCTURA DE ARCHIVOS")
    print("-" * 50)
    
    important_files = [
        ("main.py", "Archivo principal del bot", True),
        ("requirements.txt", "Lista de dependencias", True),
        (".env", "Variables de entorno", True),
        ("data/", "Directorio de datos", False),
        ("data/servers/", "Datos de servidores", False)
    ]
    
    missing_files = []
    
    for file_path, desc, required in important_files:
        if os.path.exists(file_path):
            print(f"✅ {desc}: {file_path}")
        else:
            print(f"❌ {desc}: {file_path} (no encontrado)")
            if required:
                missing_files.append(file_path)
    
    return missing_files

def test_crypto_functionality():
    """Prueba funcionalidades básicas de criptografía"""
    print("\n🧪 PRUEBAS DE FUNCIONALIDAD CRIPTOGRÁFICA")
    print("-" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Hashing con bcrypt
    try:
        import bcrypt
        password = b"test_password"
        hashed = bcrypt.hashpw(password, bcrypt.gensalt())
        if bcrypt.checkpw(password, hashed):
            print("✅ Bcrypt hashing/verificación")
            tests_passed += 1
        else:
            print("❌ Bcrypt verificación falló")
        total_tests += 1
    except Exception as e:
        print(f"❌ Bcrypt test falló: {e}")
        total_tests += 1
    
    # Test 2: Encriptación con cryptography
    try:
        from cryptography.fernet import Fernet
        key = Fernet.generate_key()
        f = Fernet(key)
        message = b"test message"
        encrypted = f.encrypt(message)
        decrypted = f.decrypt(encrypted)
        if decrypted == message:
            print("✅ Cryptography encriptación/desencriptación")
            tests_passed += 1
        else:
            print("❌ Cryptography desencriptación falló")
        total_tests += 1
    except Exception as e:
        print(f"❌ Cryptography test falló: {e}")
        total_tests += 1
    
    # Test 3: JWT tokens
    try:
        import jwt
        payload = {"test": "data"}
        secret = "test_secret"
        token = jwt.encode(payload, secret, algorithm="HS256")
        decoded = jwt.decode(token, secret, algorithms=["HS256"])
        if decoded == payload:
            print("✅ JWT encoding/decoding")
            tests_passed += 1
        else:
            print("❌ JWT decoding falló")
        total_tests += 1
    except Exception as e:
        print(f"❌ JWT test falló: {e}")
        total_tests += 1
    
    print(f"\n📊 Pruebas criptográficas: {tests_passed}/{total_tests} pasaron")
    return tests_passed == total_tests

def generate_report():
    """Genera un reporte completo"""
    print("\n" + "=" * 70)
    print("📋 REPORTE DE VERIFICACIÓN")
    print("=" * 70)
    
    # Verificar todas las categorías
    core_ok = check_core_dependencies()
    crypto_ok = check_crypto_dependencies()
    db_ok = check_database_dependencies()
    optional_count = check_optional_dependencies()
    missing_required_env, missing_optional_env = check_environment_variables()
    missing_files = check_file_structure()
    crypto_tests_ok = test_crypto_functionality()
    
    print("\n" + "=" * 70)
    print("📊 RESUMEN FINAL")
    print("=" * 70)
    
    # Estado general
    critical_issues = []
    warnings = []
    
    if not core_ok:
        critical_issues.append("Dependencias principales del bot")
    if not crypto_ok:
        critical_issues.append("Dependencias de criptografía")
    if not db_ok:
        critical_issues.append("Dependencias de base de datos")
    if missing_required_env:
        critical_issues.append(f"Variables de entorno requeridas: {', '.join(missing_required_env)}")
    if missing_files:
        critical_issues.append(f"Archivos requeridos: {', '.join(missing_files)}")
    if not crypto_tests_ok:
        critical_issues.append("Pruebas de funcionalidad criptográfica")
    
    if missing_optional_env:
        warnings.append(f"Variables opcionales: {', '.join(missing_optional_env)}")
    if optional_count < 10:
        warnings.append(f"Solo {optional_count} dependencias opcionales instaladas")
    
    # Mostrar resultados
    if not critical_issues:
        print("🎉 ¡INSTALACIÓN COMPLETAMENTE EXITOSA!")
        print("✅ Todas las dependencias críticas están instaladas")
        print("✅ Todas las pruebas de funcionalidad pasaron")
        print("✅ El bot está listo para usar cuentas encriptadas")
    else:
        print("⚠️ INSTALACIÓN INCOMPLETA")
        print("\n❌ Problemas críticos encontrados:")
        for issue in critical_issues:
            print(f"   • {issue}")
    
    if warnings:
        print("\n⚠️ Advertencias:")
        for warning in warnings:
            print(f"   • {warning}")
    
    print("\n💡 Próximos pasos:")
    if critical_issues:
        print("   1. Soluciona los problemas críticos listados arriba")
        print("   2. Ejecuta este script nuevamente para verificar")
    else:
        print("   1. Configura tus variables de entorno en .env")
        print("   2. Ejecuta el bot: python main.py")
        print("   3. Prueba las funciones de cuentas encriptadas")
    
    print("\n" + "=" * 70)

def main():
    """Función principal"""
    print_header()
    generate_report()

if __name__ == "__main__":
    main()
