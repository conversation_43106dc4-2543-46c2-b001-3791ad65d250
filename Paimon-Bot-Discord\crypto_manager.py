#!/usr/bin/env python3
"""
Sistema de Cifrado Seguro para Paimon Bot
Implementa cifrado AES-256-GCM con PBKDF2 para datos de usuario
"""

import os
import json
import hashlib
import secrets
import base64
import asyncio
from datetime import datetime
from typing import Dict, Optional, Tuple, Any
try:
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.ciphers.aead import AESGCM
    from cryptography.hazmat.backends import default_backend
    CRYPTO_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ cryptography no disponible: {e}")
    CRYPTO_AVAILABLE = False

class PaimonCryptoManager:
    """Gestor de cifrado seguro para datos de usuario con almacenamiento en GitHub"""

    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.salt_size = 16  # 16 bytes para salt
        self.iv_size = 12    # 12 bytes para IV (GCM)
        self.tag_size = 16   # 16 bytes para Auth Tag
        self.key_size = 32   # 32 bytes para AES-256
        self.iterations = 100000  # 100,000 iteraciones PBKDF2

        # Configuración de GitHub
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.github_owner = os.getenv('GITHUB_REPO_OWNER')
        self.github_repo = os.getenv('GITHUB_REPO_NAME')
        self.github_branch = os.getenv('GITHUB_BRANCH', 'main')
        self.github_folder = 'data'  # Usar carpeta data/ como en tu repositorio

        # Verificar configuración de GitHub
        self.github_available = all([self.github_token, self.github_owner, self.github_repo])

        if self.github_available:
            print("✅ GitHub configurado como almacenamiento principal")
        else:
            print("⚠️ GitHub no configurado - usando almacenamiento local como respaldo")
            self._ensure_data_directory()

    def _ensure_data_directory(self):
        """Crear directorio data/ si no existe"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            print(f"📁 Directorio {self.data_dir}/ creado")

    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derivar clave usando PBKDF2 con SHA-256"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_size,
            salt=salt,
            iterations=self.iterations,
            backend=default_backend()
        )
        return kdf.derive(password.encode('utf-8'))

    def _get_user_file_path(self, user_id: str) -> str:
        """Obtener ruta del archivo .Paimon para un usuario (local)"""
        return os.path.join(self.data_dir, f"{user_id}.Paimon")

    def _get_github_file_path(self, user_id: str) -> str:
        """Obtener ruta del archivo en GitHub"""
        return f"{self.github_folder}/{user_id}.Paimon"

    async def _github_request(self, method: str, url: str, data: dict = None) -> tuple:
        """Realizar petición HTTP a GitHub API"""
        if not self.github_available:
            return False, "GitHub no configurado"

        try:
            # Importar requests bajo demanda
            import requests

            headers = {
                'Authorization': f'token {self.github_token}',
                'Accept': 'application/vnd.github.v3+json',
                'Content-Type': 'application/json'
            }

            if method.upper() == 'GET':
                response = requests.get(url, headers=headers)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                return False, f"Método HTTP no soportado: {method}"

            return response.status_code in [200, 201], response

        except Exception as e:
            print(f"❌ Error en petición GitHub: {e}")
            return False, str(e)

    async def _save_to_github(self, user_id: str, file_data: bytes) -> bool:
        """Guardar archivo cifrado en GitHub"""
        if not self.github_available:
            return False

        try:
            # Codificar datos en base64 para GitHub
            content_b64 = base64.b64encode(file_data).decode('utf-8')

            # Ruta del archivo en GitHub
            file_path = self._get_github_file_path(user_id)

            # URL de la API de GitHub
            url = f"https://api.github.com/repos/{self.github_owner}/{self.github_repo}/contents/{file_path}"

            # Verificar si el archivo ya existe para obtener SHA
            success, response = await self._github_request('GET', url)

            payload = {
                'message': f'Update encrypted user data for {user_id}',
                'content': content_b64,
                'branch': self.github_branch
            }

            # Si el archivo existe, agregar SHA para actualización
            if success and hasattr(response, 'json'):
                try:
                    existing_data = response.json()
                    payload['sha'] = existing_data.get('sha')
                except:
                    pass

            # Guardar/actualizar archivo
            success, response = await self._github_request('PUT', url, payload)

            if success:
                print(f"✅ Usuario {user_id} guardado en GitHub exitosamente")
                return True
            else:
                print(f"❌ Error guardando en GitHub: {response}")
                return False

        except Exception as e:
            print(f"❌ Error guardando en GitHub: {e}")
            return False

    async def _load_from_github(self, user_id: str) -> Optional[bytes]:
        """Cargar archivo cifrado desde GitHub"""
        if not self.github_available:
            return None

        try:
            # Ruta del archivo en GitHub
            file_path = self._get_github_file_path(user_id)

            # URL de la API de GitHub
            url = f"https://api.github.com/repos/{self.github_owner}/{self.github_repo}/contents/{file_path}"

            # Obtener archivo
            success, response = await self._github_request('GET', url)

            if success and hasattr(response, 'json'):
                try:
                    file_data = response.json()
                    content_b64 = file_data.get('content', '')

                    # Decodificar de base64
                    file_bytes = base64.b64decode(content_b64)
                    return file_bytes

                except Exception as e:
                    print(f"❌ Error decodificando archivo de GitHub: {e}")
                    return None
            else:
                # Archivo no existe
                return None

        except Exception as e:
            print(f"❌ Error cargando desde GitHub: {e}")
            return None

    async def _delete_from_github(self, user_id: str) -> bool:
        """Eliminar archivo cifrado de GitHub"""
        if not self.github_available:
            return False

        try:
            # Ruta del archivo en GitHub
            file_path = self._get_github_file_path(user_id)

            # URL de la API de GitHub
            url = f"https://api.github.com/repos/{self.github_owner}/{self.github_repo}/contents/{file_path}"

            # Obtener SHA del archivo
            success, response = await self._github_request('GET', url)

            if not success:
                return False  # Archivo no existe

            try:
                file_data = response.json()
                sha = file_data.get('sha')

                if not sha:
                    return False

                # Eliminar archivo
                payload = {
                    'message': f'Delete encrypted user data for {user_id}',
                    'sha': sha,
                    'branch': self.github_branch
                }

                success, response = await self._github_request('DELETE', url, payload)

                if success:
                    print(f"✅ Usuario {user_id} eliminado de GitHub exitosamente")
                    return True
                else:
                    print(f"❌ Error eliminando de GitHub: {response}")
                    return False

            except Exception as e:
                print(f"❌ Error procesando eliminación: {e}")
                return False

        except Exception as e:
            print(f"❌ Error eliminando de GitHub: {e}")
            return False

    async def encrypt_user_data(self, user_id: str, username: str, password: str,
                               additional_data: Dict[str, Any] = None) -> bool:
        """
        Cifrar y guardar datos de usuario

        Args:
            user_id: ID único del usuario
            username: Nombre de usuario
            password: Contraseña del usuario
            additional_data: Datos adicionales a cifrar

        Returns:
            bool: True si el cifrado fue exitoso
        """
        try:
            # Generar salt e IV aleatorios
            salt = secrets.token_bytes(self.salt_size)
            iv = secrets.token_bytes(self.iv_size)

            # Derivar clave maestra
            key = self._derive_key(password, salt)

            # Preparar datos a cifrar
            user_data = {
                'user_id': user_id,
                'username': username,
                'password_hash': hashlib.sha256(password.encode()).hexdigest(),
                'created_at': datetime.now().isoformat(),
                'last_login': None,
                'login_count': 0,
                'additional_data': additional_data or {}
            }

            # Convertir a JSON y cifrar
            plaintext = json.dumps(user_data, ensure_ascii=False).encode('utf-8')

            # Cifrar con AES-GCM
            aesgcm = AESGCM(key)
            ciphertext = aesgcm.encrypt(iv, plaintext, None)

            # El ciphertext de AESGCM ya incluye el auth tag al final
            # Estructura del archivo: SALT (16) + IV (12) + CIPHERTEXT_WITH_TAG
            file_data = salt + iv + ciphertext

            # Guardar en GitHub como almacenamiento principal
            if self.github_available:
                success = await self._save_to_github(user_id, file_data)
                if success:
                    # Limpiar clave de memoria
                    key = b'\x00' * len(key)
                    print(f"🔐 Usuario {username} registrado y cifrado en GitHub exitosamente")
                    return True
                else:
                    print("⚠️ Error guardando en GitHub, intentando almacenamiento local...")

            # Respaldo local si GitHub falla o no está disponible
            try:
                file_path = self._get_user_file_path(user_id)
                with open(file_path, 'wb') as f:
                    f.write(file_data)

                # Limpiar clave de memoria
                key = b'\x00' * len(key)

                storage_type = "local (respaldo)" if self.github_available else "local"
                print(f"🔐 Usuario {username} registrado y cifrado en almacenamiento {storage_type} exitosamente")
                return True

            except Exception as local_error:
                print(f"❌ Error en almacenamiento local: {local_error}")
                return False

        except Exception as e:
            print(f"❌ Error cifrando datos de usuario: {e}")
            return False

    async def decrypt_user_data(self, user_id: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Descifrar y validar datos de usuario

        Args:
            user_id: ID único del usuario
            password: Contraseña para descifrar

        Returns:
            Dict con datos del usuario o None si falla
        """
        try:
            file_data = None

            # Intentar cargar desde GitHub primero
            if self.github_available:
                file_data = await self._load_from_github(user_id)
                if file_data:
                    print(f"📥 Datos cargados desde GitHub para usuario {user_id}")

            # Si no se pudo cargar desde GitHub, intentar local
            if file_data is None:
                file_path = self._get_user_file_path(user_id)

                # Verificar que el archivo local existe
                if not os.path.exists(file_path):
                    return None

                # Leer archivo .Paimon local
                with open(file_path, 'rb') as f:
                    file_data = f.read()

                storage_type = "local (respaldo)" if self.github_available else "local"
                print(f"📥 Datos cargados desde almacenamiento {storage_type} para usuario {user_id}")

            if file_data is None:
                return None

            # Verificar tamaño mínimo
            min_size = self.salt_size + self.iv_size + self.tag_size
            if len(file_data) < min_size:
                raise ValueError("Archivo corrupto: tamaño insuficiente")

            # Extraer componentes
            salt = file_data[:self.salt_size]
            iv = file_data[self.salt_size:self.salt_size + self.iv_size]
            ciphertext = file_data[self.salt_size + self.iv_size:]

            # Derivar clave con la misma sal
            key = self._derive_key(password, salt)

            # Descifrar con AES-GCM (incluye validación de integridad)
            aesgcm = AESGCM(key)
            plaintext = aesgcm.decrypt(iv, ciphertext, None)

            # Parsear JSON
            user_data = json.loads(plaintext.decode('utf-8'))

            # Validar contraseña
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if user_data.get('password_hash') != password_hash:
                raise ValueError("Contraseña incorrecta")

            # Actualizar último login
            user_data['last_login'] = datetime.now().isoformat()
            user_data['login_count'] = user_data.get('login_count', 0) + 1

            # Limpiar clave de memoria
            key = b'\x00' * len(key)

            return user_data

        except Exception as e:
            print(f"❌ Error descifrando datos de usuario: {e}")
            return None

    def change_password(self, user_id: str, old_password: str, new_password: str) -> bool:
        """
        Cambiar contraseña de usuario (rotación de clave)

        Args:
            user_id: ID único del usuario
            old_password: Contraseña actual
            new_password: Nueva contraseña

        Returns:
            bool: True si el cambio fue exitoso
        """
        try:
            # Descifrar con contraseña vieja
            user_data = self.decrypt_user_data(user_id, old_password)
            if not user_data:
                return False

            # Actualizar hash de contraseña
            user_data['password_hash'] = hashlib.sha256(new_password.encode()).hexdigest()
            user_data['password_changed_at'] = datetime.now().isoformat()

            # Volver a cifrar con nueva contraseña
            username = user_data.get('username', 'unknown')
            additional_data = user_data.get('additional_data', {})

            return self.encrypt_user_data(user_id, username, new_password, additional_data)

        except Exception as e:
            print(f"❌ Error cambiando contraseña: {e}")
            return False

    async def user_exists(self, user_id: str) -> bool:
        """Verificar si un usuario ya está registrado"""
        # Verificar en GitHub primero
        if self.github_available:
            file_data = await self._load_from_github(user_id)
            if file_data is not None:
                return True

        # Verificar en almacenamiento local
        file_path = self._get_user_file_path(user_id)
        return os.path.exists(file_path)

    def delete_user(self, user_id: str, password: str) -> bool:
        """
        Eliminar usuario (requiere contraseña para verificación)

        Args:
            user_id: ID único del usuario
            password: Contraseña para verificar identidad

        Returns:
            bool: True si la eliminación fue exitosa
        """
        try:
            # Verificar contraseña antes de eliminar
            user_data = self.decrypt_user_data(user_id, password)
            if not user_data:
                return False

            # Eliminar archivo
            file_path = self._get_user_file_path(user_id)
            os.remove(file_path)

            print(f"🗑️ Usuario {user_data.get('username')} eliminado exitosamente")
            return True

        except Exception as e:
            print(f"❌ Error eliminando usuario: {e}")
            return False

    def get_user_stats(self) -> Dict[str, Any]:
        """Obtener estadísticas de usuarios registrados"""
        try:
            files = [f for f in os.listdir(self.data_dir) if f.endswith('.Paimon')]

            return {
                'total_users': len(files),
                'data_directory': self.data_dir,
                'encryption_method': 'AES-256-GCM + PBKDF2-SHA256',
                'iterations': self.iterations,
                'files': files
            }

        except Exception as e:
            print(f"❌ Error obteniendo estadísticas: {e}")
            return {'total_users': 0, 'error': str(e)}

    def backup_to_github(self, github_token: str, repo_owner: str, repo_name: str) -> bool:
        """
        Hacer backup de todos los archivos .Paimon a GitHub

        Args:
            github_token: Token de acceso a GitHub
            repo_owner: Propietario del repositorio
            repo_name: Nombre del repositorio

        Returns:
            bool: True si el backup fue exitoso
        """
        try:
            import requests
            import base64

            files = [f for f in os.listdir(self.data_dir) if f.endswith('.Paimon')]

            if not files:
                print("📁 No hay archivos .Paimon para respaldar")
                return True

            headers = {
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            success_count = 0

            for file_name in files:
                try:
                    file_path = os.path.join(self.data_dir, file_name)

                    # Leer archivo cifrado
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    # Codificar en base64 para GitHub
                    encoded_content = base64.b64encode(file_content).decode('utf-8')

                    # Verificar si el archivo ya existe en GitHub
                    github_path = f"encrypted_users/{file_name}"
                    url = f'https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{github_path}'

                    response = requests.get(url, headers=headers)

                    payload = {
                        'message': f'Backup cifrado: {file_name}',
                        'content': encoded_content,
                        'branch': 'main'
                    }

                    # Si el archivo existe, necesitamos el SHA
                    if response.status_code == 200:
                        existing_data = response.json()
                        payload['sha'] = existing_data['sha']

                    # Subir/actualizar archivo
                    response = requests.put(url, headers=headers, json=payload)

                    if response.status_code in [200, 201]:
                        success_count += 1
                        print(f"✅ {file_name} respaldado en GitHub")
                    else:
                        print(f"❌ Error respaldando {file_name}: {response.status_code}")

                except Exception as e:
                    print(f"❌ Error procesando {file_name}: {e}")

            print(f"📦 Backup completado: {success_count}/{len(files)} archivos")
            return success_count == len(files)

        except Exception as e:
            print(f"❌ Error en backup a GitHub: {e}")
            return False

    def restore_from_github(self, github_token: str, repo_owner: str, repo_name: str) -> bool:
        """
        Restaurar archivos .Paimon desde GitHub

        Args:
            github_token: Token de acceso a GitHub
            repo_owner: Propietario del repositorio
            repo_name: Nombre del repositorio

        Returns:
            bool: True si la restauración fue exitosa
        """
        try:
            import requests
            import base64

            headers = {
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            # Listar archivos en el directorio encrypted_users
            url = f'https://api.github.com/repos/{repo_owner}/{repo_name}/contents/encrypted_users'
            response = requests.get(url, headers=headers)

            if response.status_code != 200:
                print("📁 No se encontró directorio encrypted_users en GitHub")
                return False

            files_data = response.json()

            if not isinstance(files_data, list):
                print("❌ Respuesta inesperada de GitHub")
                return False

            success_count = 0

            for file_info in files_data:
                if file_info['name'].endswith('.Paimon'):
                    try:
                        # Descargar archivo
                        file_response = requests.get(file_info['download_url'], headers=headers)

                        if file_response.status_code == 200:
                            # Decodificar contenido
                            encoded_content = file_response.json()['content']
                            file_content = base64.b64decode(encoded_content)

                            # Guardar archivo local
                            local_path = os.path.join(self.data_dir, file_info['name'])
                            with open(local_path, 'wb') as f:
                                f.write(file_content)

                            success_count += 1
                            print(f"✅ {file_info['name']} restaurado desde GitHub")
                        else:
                            print(f"❌ Error descargando {file_info['name']}")

                    except Exception as e:
                        print(f"❌ Error procesando {file_info['name']}: {e}")

            print(f"📥 Restauración completada: {success_count} archivos")
            return success_count > 0

        except Exception as e:
            print(f"❌ Error en restauración desde GitHub: {e}")
            return False

class PaimonCryptoManagerSync:
    """Wrapper síncrono para el gestor de cifrado async"""

    def __init__(self):
        self._async_manager = PaimonCryptoManager() if CRYPTO_AVAILABLE else None

    def _run_async(self, coro):
        """Ejecutar función async de forma síncrona"""
        try:
            import asyncio
            # Simplemente usar asyncio.run para ejecutar la corrutina
            return asyncio.run(coro)
        except Exception as e:
            print(f"❌ Error ejecutando función async: {e}")
            return None

    def user_exists(self, user_id: str) -> bool:
        """Verificar si un usuario ya está registrado (versión síncrona)"""
        if not self._async_manager:
            return False

        try:
            result = self._run_async(self._async_manager.user_exists(user_id))
            return result if isinstance(result, bool) else False
        except Exception as e:
            print(f"❌ Error verificando usuario: {e}")
            return False

    def encrypt_user_data(self, user_id: str, username: str, password: str,
                         additional_data: Dict[str, Any] = None) -> bool:
        """Cifrar y guardar datos de usuario (versión síncrona)"""
        if not self._async_manager:
            return False

        try:
            result = self._run_async(
                self._async_manager.encrypt_user_data(user_id, username, password, additional_data)
            )
            return result if isinstance(result, bool) else False
        except Exception as e:
            print(f"❌ Error cifrando datos: {e}")
            return False

    def decrypt_user_data(self, user_id: str, password: str) -> Optional[Dict[str, Any]]:
        """Descifrar y validar datos de usuario (versión síncrona)"""
        if not self._async_manager:
            return None

        try:
            return self._run_async(self._async_manager.decrypt_user_data(user_id, password))
        except Exception as e:
            print(f"❌ Error descifrando datos: {e}")
            return None

    def get_user_stats(self) -> Dict[str, Any]:
        """Obtener estadísticas de usuarios registrados"""
        if not self._async_manager:
            return {'total_users': 0, 'error': 'crypto_manager no disponible', 'crypto_available': False}

        try:
            return self._run_async(self._async_manager.get_user_stats())
        except Exception as e:
            print(f"❌ Error obteniendo estadísticas: {e}")
            return {'total_users': 0, 'error': str(e), 'crypto_available': CRYPTO_AVAILABLE}

# Instancia global del gestor de cifrado
if CRYPTO_AVAILABLE:
    crypto_manager = PaimonCryptoManagerSync()
    print("✅ crypto_manager inicializado correctamente")
else:
    crypto_manager = None
    print("❌ crypto_manager no disponible - instala cryptography")
