#!/usr/bin/env python3
"""
Script para eliminar comandos de testing redundantes del bot
Mantiene solo el comando !testing unificado
"""

import re
import os
from datetime import datetime

def find_test_commands(file_path):
    """Encuentra todos los comandos de testing en el archivo"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Patrón para encontrar comandos que empiecen con "test"
    pattern = r'@bot\.command\(name="test[^"]*"[^)]*\)[^@]*?(?=@bot\.command|$)'
    
    matches = re.finditer(pattern, content, re.DOTALL)
    
    test_commands = []
    for match in matches:
        command_text = match.group(0)
        start_pos = match.start()
        end_pos = match.end()
        
        # Extraer el nombre del comando
        name_match = re.search(r'name="(test[^"]*)"', command_text)
        if name_match:
            command_name = name_match.group(1)
            test_commands.append({
                'name': command_name,
                'start': start_pos,
                'end': end_pos,
                'text': command_text
            })
    
    return test_commands

def remove_test_commands(file_path, backup=True):
    """Elimina todos los comandos de testing del archivo"""
    
    # Crear backup si se solicita
    if backup:
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(file_path, 'r', encoding='utf-8') as original:
            with open(backup_path, 'w', encoding='utf-8') as backup_file:
                backup_file.write(original.read())
        print(f"✅ Backup creado: {backup_path}")
    
    # Leer archivo original
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Encontrar comandos de testing
    test_commands = find_test_commands(file_path)
    
    if not test_commands:
        print("❌ No se encontraron comandos de testing para eliminar")
        return
    
    print(f"🔍 Encontrados {len(test_commands)} comandos de testing:")
    for cmd in test_commands:
        print(f"   • {cmd['name']}")
    
    # Eliminar comandos en orden inverso para mantener posiciones correctas
    test_commands.sort(key=lambda x: x['start'], reverse=True)
    
    new_content = content
    removed_count = 0
    
    for cmd in test_commands:
        # Eliminar el comando completo
        before = new_content[:cmd['start']]
        after = new_content[cmd['end']:]
        
        # Limpiar líneas vacías extra
        if before.endswith('\n\n\n'):
            before = before.rstrip('\n') + '\n\n'
        
        new_content = before + after
        removed_count += 1
        print(f"🗑️ Eliminado: {cmd['name']}")
    
    # Escribir archivo modificado
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"\n✅ Eliminados {removed_count} comandos de testing")
    print(f"📁 Archivo actualizado: {file_path}")
    
    return removed_count

def analyze_file(file_path):
    """Analiza el archivo y muestra estadísticas"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Contar todos los comandos
    all_commands = re.findall(r'@bot\.command\(name="([^"]*)"', content)
    test_commands = [cmd for cmd in all_commands if cmd.startswith('test')]
    
    print("📊 ANÁLISIS DEL ARCHIVO")
    print("=" * 50)
    print(f"📁 Archivo: {file_path}")
    print(f"📏 Líneas totales: {len(content.splitlines())}")
    print(f"⚙️ Comandos totales: {len(all_commands)}")
    print(f"🧪 Comandos de testing: {len(test_commands)}")
    print(f"✅ Comandos funcionales: {len(all_commands) - len(test_commands)}")
    
    if test_commands:
        print(f"\n🧪 Comandos de testing encontrados:")
        for i, cmd in enumerate(test_commands, 1):
            print(f"   {i:2d}. {cmd}")
    
    return len(all_commands), len(test_commands)

def main():
    """Función principal"""
    print("🧹 LIMPIEZA DE COMANDOS DE TESTING")
    print("=" * 50)
    
    file_path = "main.py"
    
    if not os.path.exists(file_path):
        print(f"❌ Error: No se encontró el archivo {file_path}")
        return
    
    # Analizar archivo antes
    print("📊 ANTES DE LA LIMPIEZA:")
    total_before, test_before = analyze_file(file_path)
    
    if test_before == 0:
        print("\n✅ No hay comandos de testing para eliminar")
        return
    
    # Confirmar eliminación
    print(f"\n⚠️ Se eliminarán {test_before} comandos de testing")
    response = input("¿Continuar? (s/N): ").lower().strip()
    
    if response not in ['s', 'si', 'sí', 'y', 'yes']:
        print("❌ Operación cancelada")
        return
    
    # Eliminar comandos
    print(f"\n🧹 ELIMINANDO COMANDOS DE TESTING...")
    removed_count = remove_test_commands(file_path, backup=True)
    
    # Analizar archivo después
    print(f"\n📊 DESPUÉS DE LA LIMPIEZA:")
    total_after, test_after = analyze_file(file_path)
    
    # Resumen final
    print(f"\n🎉 LIMPIEZA COMPLETADA")
    print("=" * 50)
    print(f"🗑️ Comandos eliminados: {removed_count}")
    print(f"📉 Comandos de testing: {test_before} → {test_after}")
    print(f"📊 Comandos totales: {total_before} → {total_after}")
    print(f"✅ Comandos funcionales: {total_after - test_after}")
    
    if test_after == 0:
        print(f"\n🎯 ¡Perfecto! Todos los comandos de testing han sido eliminados")
        print(f"💡 Ahora usa el comando unificado: !testing")
    else:
        print(f"\n⚠️ Quedan {test_after} comandos de testing")
    
    print(f"\n💾 El archivo original se respaldó automáticamente")

if __name__ == "__main__":
    main()
