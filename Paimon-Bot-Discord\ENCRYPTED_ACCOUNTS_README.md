# 🔐 Cuentas Encriptadas - Paimon <PERSON>t

## 📋 Descripción

El sistema de **Cuentas Encriptadas** de Paimon Bot proporciona una capa adicional de seguridad para proteger datos sensibles de usuarios, incluyendo:

- 🔑 Autenticación segura con múltiples factores
- 🛡️ Encriptación de datos personales
- 🔐 Hashing seguro de contraseñas
- 🎫 Tokens JWT para sesiones
- 📊 Auditoría de accesos

## 🚀 Instalación Rápida

### 1. Ejecutar el Instalador Automático
```bash
python install_dependencies.py
```

### 2. Verificar la Instalación
```bash
python verify_installation.py
```

### 3. Configurar Variables de Entorno
```bash
cp .env.example .env
# Edita .env con tus valores reales
```

## 📦 Dependencias Requeridas

### 🔐 Criptografía Principal
```bash
pip install cryptography>=41.0.0
pip install bcrypt>=4.0.0
pip install passlib[bcrypt,argon2]>=1.7.4
pip install argon2-cffi>=23.0.0
pip install pycryptodome>=3.19.0
```

### 🎫 Autenticación y Tokens
```bash
pip install PyJWT>=2.8.0
pip install python-jose[cryptography]>=3.3.0
pip install itsdangerous>=2.1.0
```

### 💾 Base de Datos Segura
```bash
pip install sqlalchemy>=2.0.0
pip install aiosqlite>=0.19.0
```

### 🔍 Validación
```bash
pip install email-validator>=2.1.0
pip install validators>=0.22.0
pip install secure>=0.3.0
```

## ⚙️ Configuración

### Variables de Entorno Requeridas

```bash
# Claves de seguridad (OBLIGATORIAS)
SECRET_KEY=tu-clave-secreta-muy-larga-y-compleja-de-al-menos-32-caracteres
ENCRYPTION_KEY=otra-clave-para-encriptacion-de-al-menos-32-caracteres
JWT_SECRET=clave-para-tokens-jwt-de-al-menos-32-caracteres
DATABASE_ENCRYPTION_KEY=clave-para-base-de-datos-de-al-menos-32-caracteres
PASSWORD_SALT=salt-para-contraseñas-unico-y-aleatorio

# Configuración de seguridad
TOKEN_EXPIRY=86400  # 24 horas
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=1800  # 30 minutos
ENABLE_2FA=true
```

### Generar Claves Seguras

```python
import secrets

# Generar claves aleatorias seguras
print("SECRET_KEY=" + secrets.token_urlsafe(32))
print("ENCRYPTION_KEY=" + secrets.token_urlsafe(32))
print("JWT_SECRET=" + secrets.token_urlsafe(32))
print("DATABASE_ENCRYPTION_KEY=" + secrets.token_urlsafe(32))
print("PASSWORD_SALT=" + secrets.token_urlsafe(16))
```

## 🛡️ Características de Seguridad

### 1. **Encriptación de Datos**
- **AES-256**: Para datos sensibles
- **Fernet**: Para encriptación simétrica
- **RSA**: Para intercambio de claves

### 2. **Hashing de Contraseñas**
- **Argon2**: Algoritmo recomendado
- **bcrypt**: Alternativa robusta
- **PBKDF2**: Compatibilidad adicional

### 3. **Autenticación**
- **JWT Tokens**: Sesiones seguras
- **2FA**: Autenticación de dos factores
- **Rate Limiting**: Protección contra ataques

### 4. **Auditoría**
- **Logs de acceso**: Registro completo
- **Intentos fallidos**: Detección de ataques
- **Actividad de usuarios**: Monitoreo continuo

## 🎯 Comandos Disponibles

### Comandos de Usuario
```bash
!register <email> <password>     # Registrar nueva cuenta
!login <email> <password>        # Iniciar sesión
!logout                          # Cerrar sesión
!changepassword <old> <new>      # Cambiar contraseña
!enable2fa                       # Activar 2FA
!disable2fa <code>               # Desactivar 2FA
!profile                         # Ver perfil encriptado
!deleteaccount <password>        # Eliminar cuenta
```

### Comandos de Administración
```bash
!listaccounts                    # Listar cuentas (admin)
!lockaccount <user>              # Bloquear cuenta (admin)
!unlockaccount <user>            # Desbloquear cuenta (admin)
!resetpassword <user>            # Resetear contraseña (admin)
!auditlogs [user]                # Ver logs de auditoría (admin)
!securitystatus                  # Estado de seguridad (admin)
```

## 🔧 Estructura de Base de Datos

### Tabla: encrypted_users
```sql
- id (INTEGER PRIMARY KEY)
- email_hash (TEXT UNIQUE)
- password_hash (TEXT)
- encrypted_data (TEXT)
- salt (TEXT)
- created_at (TIMESTAMP)
- last_login (TIMESTAMP)
- failed_attempts (INTEGER)
- locked_until (TIMESTAMP)
- two_factor_secret (TEXT)
- is_active (BOOLEAN)
```

### Tabla: audit_logs
```sql
- id (INTEGER PRIMARY KEY)
- user_id (INTEGER)
- action (TEXT)
- ip_address (TEXT)
- user_agent (TEXT)
- timestamp (TIMESTAMP)
- success (BOOLEAN)
- details (TEXT)
```

## 🧪 Pruebas de Funcionalidad

### Ejecutar Pruebas
```bash
python -m pytest tests/test_crypto.py -v
```

### Pruebas Manuales
```python
# Test de encriptación
from crypto_utils import encrypt_data, decrypt_data
data = "información sensible"
encrypted = encrypt_data(data)
decrypted = decrypt_data(encrypted)
assert data == decrypted

# Test de hashing
from crypto_utils import hash_password, verify_password
password = "mi_contraseña_segura"
hashed = hash_password(password)
assert verify_password(password, hashed)

# Test de JWT
from crypto_utils import create_jwt_token, verify_jwt_token
payload = {"user_id": 123, "email": "<EMAIL>"}
token = create_jwt_token(payload)
decoded = verify_jwt_token(token)
assert decoded["user_id"] == 123
```

## 🚨 Consideraciones de Seguridad

### ✅ Buenas Prácticas
- ✅ Usar claves largas y aleatorias
- ✅ Cambiar claves regularmente
- ✅ Habilitar 2FA para administradores
- ✅ Monitorear logs de auditoría
- ✅ Hacer backups encriptados
- ✅ Usar HTTPS en producción

### ❌ Evitar
- ❌ Hardcodear claves en el código
- ❌ Usar contraseñas débiles
- ❌ Compartir claves de encriptación
- ❌ Ignorar logs de seguridad
- ❌ Desactivar validaciones

## 🔍 Troubleshooting

### Error: "cryptography not found"
```bash
pip install --upgrade cryptography
# En Windows, puede requerir Visual Studio Build Tools
```

### Error: "bcrypt compilation failed"
```bash
# Linux/Ubuntu
sudo apt install build-essential libffi-dev python3-dev
pip install bcrypt

# macOS
xcode-select --install
pip install bcrypt
```

### Error: "JWT decode error"
```bash
# Verificar que JWT_SECRET esté configurado
echo $JWT_SECRET
# Regenerar clave si es necesario
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

## 📊 Monitoreo y Métricas

### Métricas Importantes
- 📈 **Intentos de login**: Exitosos vs fallidos
- 🔒 **Cuentas bloqueadas**: Por intentos fallidos
- 🕒 **Tiempo de sesión**: Duración promedio
- 🛡️ **Eventos de seguridad**: Accesos sospechosos

### Alertas Configurables
- 🚨 **Múltiples intentos fallidos**
- 🚨 **Login desde IP desconocida**
- 🚨 **Cambios de contraseña frecuentes**
- 🚨 **Acceso a datos sensibles**

## 📞 Soporte

### Documentación Adicional
- 📖 [Guía de Seguridad](SECURITY.md)
- 📖 [API Reference](API_REFERENCE.md)
- 📖 [Troubleshooting](TROUBLESHOOTING.md)

### Contacto
- 🐛 **Bugs**: Crear issue en GitHub
- 💬 **Preguntas**: Discord server
- 📧 **Seguridad**: <EMAIL>

---

## ⚡ Inicio Rápido

```bash
# 1. Clonar repositorio
git clone https://github.com/Fl0zWer/DiscordBot.git
cd DiscordBot

# 2. Instalar dependencias
python install_dependencies.py

# 3. Configurar variables
cp .env.example .env
# Editar .env con tus valores

# 4. Verificar instalación
python verify_installation.py

# 5. Ejecutar bot
python main.py
```

¡Tu sistema de cuentas encriptadas estará listo para usar! 🎉
