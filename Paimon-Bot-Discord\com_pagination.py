#!/usr/bin/env python3
"""
Nueva implementación del comando com con paginación
"""

import discord
from discord.ui import View

class CommandsPaginationView(View):
    def __init__(self, pages, author):
        super().__init__(timeout=300)  # 5 minutos de timeout
        self.pages = pages
        self.current_page = 0
        self.author = author

    @discord.ui.button(label="◀️ Anterior", style=discord.ButtonStyle.gray, disabled=True)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.author:
            await interaction.response.send_message("❌ Solo quien ejecutó el comando puede usar estos botones.", ephemeral=True)
            return

        self.current_page = (self.current_page - 1) % len(self.pages)
        await self.update_buttons()
        await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(label="▶️ Siguiente", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.author:
            await interaction.response.send_message("❌ Solo quien ejecutó el comando puede usar estos botones.", ephemeral=True)
            return

        self.current_page = (self.current_page + 1) % len(self.pages)
        await self.update_buttons()
        await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(label="🏠 Inicio", style=discord.ButtonStyle.blurple)
    async def home_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.author:
            await interaction.response.send_message("❌ Solo quien ejecutó el comando puede usar estos botones.", ephemeral=True)
            return

        self.current_page = 0
        await self.update_buttons()
        await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(label="❌ Cerrar", style=discord.ButtonStyle.red)
    async def close_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.author:
            await interaction.response.send_message("❌ Solo quien ejecutó el comando puede usar estos botones.", ephemeral=True)
            return

        await interaction.message.delete()

    async def update_buttons(self):
        # Actualizar estado de botones
        self.previous_button.disabled = self.current_page == 0
        self.next_button.disabled = self.current_page == len(self.pages) - 1

        # Actualizar etiquetas con números de página
        self.previous_button.label = f"◀️ Anterior ({self.current_page}/{len(self.pages)-1})"
        self.next_button.label = f"▶️ Siguiente ({self.current_page + 2}/{len(self.pages)})"
        self.home_button.label = f"🏠 Inicio (1/{len(self.pages)})"

def get_emoji(emoji_type, animated=False):
    """Función auxiliar para obtener emojis"""
    emojis = {
        'level': '🎮',
        'star': '⭐',
        'config': '⚙️',
        'admin': '🛡️',
        'error': '❌',
        'fun': '🎭',
        'mod': '🔒'
    }
    return emojis.get(emoji_type, '📋')

def create_enhanced_embed(title, description=None, color='primary', thumbnail=None, image=None, footer_text=None, author_name=None, author_icon=None):
    """Función auxiliar para crear embeds"""
    colors = {
        'primary': 0x3498db,
        'success': 0x2ecc71,
        'warning': 0xf39c12,
        'error': 0xe74c3c,
        'info': 0x3498db,
        'level': 0x9b59b6,
        'star': 0xf1c40f,
        'config': 0x34495e,
        'admin': 0xe67e22,
        'fun': 0xe91e63,
        'mod': 0x8e44ad
    }

    embed = discord.Embed(
        title=title,
        description=description,
        color=colors.get(color, 0x3498db)
    )

    if thumbnail:
        embed.set_thumbnail(url=thumbnail)
    if image:
        embed.set_image(url=image)
    if footer_text:
        embed.set_footer(text=footer_text)
    if author_name:
        embed.set_author(name=author_name, icon_url=author_icon)

    return embed

async def com_command_with_pagination(ctx, bot):
    """Nueva implementación del comando com con paginación"""
    try:
        # Definir las páginas de comandos
        pages = []

        # Página 1: Gestión de Niveles
        embed1 = create_enhanced_embed(
            title="📋 Comandos - Página 1/5",
            description="**Gestión de Niveles** - Comandos para manejar la cola de niveles",
            color='level',
            thumbnail=bot.user.avatar.url if bot.user.avatar else None
        )

        level_cmds = [
            "`!request` - Solicita información de un nivel",
            "`!queue` - Muestra la cola de niveles",
            "`!skip` - Salta al siguiente nivel (admin)",
            "`!remove` - Elimina un nivel de la cola",
            "`!nowplaying` - Muestra el nivel actual",
            "`!clearqueue` - Borra toda la cola (admin)",
            "`!clear` - Borra información de niveles (admin)",
            "`!stage` - Muestra niveles en el canal stage (admin)",
            "`!del` - Elimina un formulario por ID",
            "`!profile` - Muestra perfil de usuario",
            "`!edit` - Edita un nivel ya agregado",
            "`!info` - Detalles completos de un nivel",
            "`!history` - Historial de niveles de usuario",
            "`!like` - Marca nivel como 'me gusta'",
            "`!random` - Nivel aleatorio de la cola",
            "`!toprequesters` - Top usuarios solicitantes",
            "`!replay` - Reproduce último nivel"
        ]

        embed1.add_field(
            name=f"{get_emoji('level')} Gestión de Niveles",
            value="\n".join(level_cmds),
            inline=False
        )
        pages.append(embed1)

        # Página 2: Sistema de Niveles/XP
        embed2 = create_enhanced_embed(
            title="📋 Comandos - Página 2/5",
            description="**Sistema de Niveles/XP** - Comandos del sistema de experiencia",
            color='star',
            thumbnail=bot.user.avatar.url if bot.user.avatar else None
        )

        xp_cmds = [
            "`!rank` - Muestra nivel y puntos de usuario",
            "`!top` - Top 10 del servidor",
            "`!levelroles` - Muestra roles por nivel (admin)",
            "`!setlevelrole` - Configura rol automático (admin)",
            "`!resetlevel` - Reinicia nivel de usuario (admin)",
            "`!addpoints` - Agrega/quita puntos (admin)",
            "`!setlevelchannel` - Canal para anuncios de nivel (admin)",
            "`!settoprole` - Rol para top 1 o top 10 (admin)",
            "`!setxpbased` - Cambia XP base diario (admin)",
            "`!setcooldown` - Tiempo entre mensajes válidos (admin)",
            "`!resetxp` - Resetea XP de un usuario (admin)"
        ]

        embed2.add_field(
            name=f"{get_emoji('star')} Sistema de Niveles/XP",
            value="\n".join(xp_cmds),
            inline=False
        )
        pages.append(embed2)

        # Página 3: Comandos Divertidos
        embed3 = create_enhanced_embed(
            title="📋 Comandos - Página 3/5",
            description="**Comandos Divertidos** - Entretenimiento y utilidades",
            color='fun',
            thumbnail=bot.user.avatar.url if bot.user.avatar else None
        )

        fun_cmds = [
            "`!8ball` - Bola mágica con respuestas",
            "`!quote` - Frase motivacional/graciosa",
            "`!gif` - Busca y responde con GIF",
            "`!dailyfact` - Dato curioso diario",
            "`!waifu` - Personaje waifu aleatorio",
            "`!husbando` - Personaje husbando aleatorio"
        ]

        embed3.add_field(
            name="🎭 Extras / Divertidos",
            value="\n".join(fun_cmds),
            inline=False
        )
        pages.append(embed3)

        # Página 4: Moderación
        embed4 = create_enhanced_embed(
            title="📋 Comandos - Página 4/5",
            description="**Moderación** - Herramientas de administración",
            color='mod',
            thumbnail=bot.user.avatar.url if bot.user.avatar else None
        )

        mod_cmds = [
            "`!setantiraid` - Activa/desactiva anti-raid (admin)",
            "`!warn` - Advertir usuario (admin)",
            "`!infractions` - Ver advertencias (admin)",
            "`!slowmode` - Modo lento canal (admin)",
            "`!lock` - Bloquear canal (admin)",
            "`!unlock` - Desbloquear canal (admin)"
        ]

        embed4.add_field(
            name="🔒 Moderación",
            value="\n".join(mod_cmds),
            inline=False
        )
        pages.append(embed4)

        # Página 5: Configuración y Gestión del Bot
        embed5 = create_enhanced_embed(
            title="📋 Comandos - Página 5/5",
            description="**Configuración y Gestión** - Ajustes del bot y servidor",
            color='config',
            thumbnail=bot.user.avatar.url if bot.user.avatar else None
        )

        config_cmds = [
            "`!Stage` - Configura canal Stage (admin)",
            "`!showstage` - Muestra canal Stage (admin)",
            "`!config` - Muestra configuración (admin)",
            "`!setconfig` - Establece configuración (admin)",
            "`!setprefix` - Cambiar prefijo (admin)",
            "`!setrole` - Rol administrativo (admin)",
            "`!setchannel` - Configurar canales (admin)",
            "`!lang` - Cambiar idioma (admin)"
        ]

        bot_cmds = [
            "`!ping` - Verifica latencia del bot",
            "`!version` - Muestra versión del bot",
            "`!restart` - Reinicia el bot (owner)",
            "`!off` - Apaga el bot (owner)"
        ]

        slash_cmds = [
            "`/paimon` - Número aleatorio",
            "`/request` - Solicitar nivel (slash)",
            "`/del` - Eliminar formulario (slash)"
        ]

        embed5.add_field(
            name=f"{get_emoji('config')} Configuración",
            value="\n".join(config_cmds),
            inline=False
        )

        embed5.add_field(
            name=f"{get_emoji('admin')} Gestión del Bot",
            value="\n".join(bot_cmds),
            inline=False
        )

        embed5.add_field(
            name="⚡ Comandos Slash",
            value="\n".join(slash_cmds),
            inline=False
        )

        embed5.add_field(
            name="🛡️ Sistema Anti-Raid",
            value="• Detección automática de spam\n• Baneos automáticos por raid\n• Límite de mensajes por tiempo",
            inline=False
        )

        embed5.set_footer(text="💡 Los comandos marcados con (admin) requieren permisos de administrador")
        pages.append(embed5)

        # Crear la vista de paginación
        view = CommandsPaginationView(pages, ctx.author)
        await ctx.send(embed=pages[0], view=view)

    except Exception as e:
        embed = create_enhanced_embed(
            title=f"{get_emoji('error')} Error",
            description=f"❌ Error al listar comandos: {e}",
            color='error'
        )
        await ctx.send(embed=embed) 