import discord
from discord.ext import commands, tasks
import aiohttp
import asyncio
import json
import sqlite3
from datetime import datetime, timedelta
import re
from typing import Dict, List, Optional, Union
import os

# Configuración de APIs
TWITCH_CLIENT_ID = os.environ.get('TWITCH_CLIENT_ID', '')
TWITCH_CLIENT_SECRET = os.environ.get('TWITCH_CLIENT_SECRET', '')
YOUTUBE_API_KEY = os.environ.get('YOUTUBE_API_KEY', '')
DISCORD_WEBHOOK_URL = os.environ.get('DISCORD_WEBHOOK_URL', '')

class FlozwerNotifications:
    def __init__(self, bot):
        self.bot = bot
        self.db_path = 'flozwer_notifications.db'
        self.init_database()
        self.check_streams.start()
        self.check_videos.start()
        
    def init_database(self):
        """Inicializa la base de datos para las notificaciones"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabla de configuración por servidor
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS server_config (
            guild_id INTEGER PRIMARY KEY,
            notification_channel_id INTEGER,
            enabled BOOLEAN DEFAULT TRUE,
            ping_role_id INTEGER,
            ping_users TEXT,  -- JSON array de user IDs
            embed_color TEXT DEFAULT '#FF6B9D',
            custom_message TEXT,
            auto_delete_minutes INTEGER DEFAULT 0
        )
        ''')
        
        # Tabla de streamers/canales monitoreados
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS monitored_channels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            guild_id INTEGER,
            platform TEXT,  -- 'twitch', 'youtube', 'tiktok', 'instagram'
            channel_id TEXT,
            channel_name TEXT,
            channel_url TEXT,
            notification_type TEXT,  -- 'stream', 'video', 'both'
            enabled BOOLEAN DEFAULT TRUE,
            custom_message TEXT,
            last_stream_id TEXT,
            last_video_id TEXT,
            last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (guild_id) REFERENCES server_config (guild_id)
        )
        ''')
        
        # Tabla de historial de notificaciones
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS notification_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            guild_id INTEGER,
            platform TEXT,
            channel_name TEXT,
            content_type TEXT,  -- 'stream', 'video'
            content_id TEXT,
            content_title TEXT,
            content_url TEXT,
            notification_sent BOOLEAN DEFAULT FALSE,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Tabla de configuraciones avanzadas
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS advanced_config (
            guild_id INTEGER PRIMARY KEY,
            cooldown_minutes INTEGER DEFAULT 5,
            max_notifications_per_hour INTEGER DEFAULT 10,
            filter_keywords TEXT,  -- JSON array de palabras clave
            exclude_keywords TEXT,  -- JSON array de palabras a excluir
            timezone TEXT DEFAULT 'UTC',
            quiet_hours_start TEXT DEFAULT '23:00',
            quiet_hours_end TEXT DEFAULT '08:00',
            auto_archive_days INTEGER DEFAULT 30
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_server_config(self, guild_id: int) -> Dict:
        """Obtiene la configuración del servidor"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT notification_channel_id, enabled, ping_role_id, ping_users, 
               embed_color, custom_message, auto_delete_minutes
        FROM server_config WHERE guild_id = ?
        ''', (guild_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'notification_channel_id': result[0],
                'enabled': bool(result[1]),
                'ping_role_id': result[2],
                'ping_users': json.loads(result[3]) if result[3] else [],
                'embed_color': result[4],
                'custom_message': result[5],
                'auto_delete_minutes': result[6]
            }
        return None
    
    def set_server_config(self, guild_id: int, **kwargs) -> bool:
        """Establece la configuración del servidor"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Preparar datos para inserción/actualización
            data = {
                'guild_id': guild_id,
                'notification_channel_id': kwargs.get('notification_channel_id'),
                'enabled': kwargs.get('enabled', True),
                'ping_role_id': kwargs.get('ping_role_id'),
                'ping_users': json.dumps(kwargs.get('ping_users', [])),
                'embed_color': kwargs.get('embed_color', '#FF6B9D'),
                'custom_message': kwargs.get('custom_message'),
                'auto_delete_minutes': kwargs.get('auto_delete_minutes', 0)
            }
            
            cursor.execute('''
            INSERT OR REPLACE INTO server_config 
            (guild_id, notification_channel_id, enabled, ping_role_id, ping_users, embed_color, custom_message, auto_delete_minutes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (data['guild_id'], data['notification_channel_id'], data['enabled'], 
                  data['ping_role_id'], data['ping_users'], data['embed_color'], 
                  data['custom_message'], data['auto_delete_minutes']))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error setting server config: {e}")
            return False
    
    def add_monitored_channel(self, guild_id: int, platform: str, channel_id: str, 
                             channel_name: str, channel_url: str, notification_type: str = 'both') -> bool:
        """Agrega un canal para monitorear"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT INTO monitored_channels 
            (guild_id, platform, channel_id, channel_name, channel_url, notification_type)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (guild_id, platform, channel_id, channel_name, channel_url, notification_type))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error adding monitored channel: {e}")
            return False
    
    def remove_monitored_channel(self, guild_id: int, platform: str, channel_id: str) -> bool:
        """Remueve un canal monitoreado"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            DELETE FROM monitored_channels 
            WHERE guild_id = ? AND platform = ? AND channel_id = ?
            ''', (guild_id, platform, channel_id))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error removing monitored channel: {e}")
            return False
    
    def get_monitored_channels(self, guild_id: int) -> List[Dict]:
        """Obtiene todos los canales monitoreados de un servidor"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT platform, channel_id, channel_name, channel_url, notification_type, enabled
        FROM monitored_channels WHERE guild_id = ?
        ''', (guild_id,))
        
        results = cursor.fetchall()
        conn.close()
        
        return [{
            'platform': r[0],
            'channel_id': r[1],
            'channel_name': r[2],
            'channel_url': r[3],
            'notification_type': r[4],
            'enabled': bool(r[5])
        } for r in results]
    
    async def get_twitch_access_token(self) -> Optional[str]:
        """Obtiene token de acceso de Twitch"""
        if not TWITCH_CLIENT_ID or not TWITCH_CLIENT_SECRET:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                data = {
                    'client_id': TWITCH_CLIENT_ID,
                    'client_secret': TWITCH_CLIENT_SECRET,
                    'grant_type': 'client_credentials'
                }
                
                async with session.post('https://id.twitch.tv/oauth2/token', data=data) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        return result.get('access_token')
        except Exception as e:
            print(f"Error getting Twitch token: {e}")
        return None
    
    async def check_twitch_stream(self, channel_id: str) -> Optional[Dict]:
        """Verifica si un canal de Twitch está en vivo"""
        token = await self.get_twitch_access_token()
        if not token:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Client-ID': TWITCH_CLIENT_ID,
                    'Authorization': f'Bearer {token}'
                }
                
                url = f'https://api.twitch.tv/helix/streams?user_id={channel_id}'
                async with session.get(url, headers=headers) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        if data.get('data'):
                            stream = data['data'][0]
                            return {
                                'id': stream['id'],
                                'title': stream['title'],
                                'game_name': stream['game_name'],
                                'viewer_count': stream['viewer_count'],
                                'started_at': stream['started_at'],
                                'thumbnail_url': stream['thumbnail_url'].replace('{width}', '1280').replace('{height}', '720'),
                                'url': f"https://twitch.tv/{stream['user_login']}"
                            }
        except Exception as e:
            print(f"Error checking Twitch stream: {e}")
        return None
    
    async def check_youtube_videos(self, channel_id: str, max_results: int = 5) -> List[Dict]:
        """Verifica videos recientes de YouTube"""
        if not YOUTUBE_API_KEY:
            return []
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f'https://www.googleapis.com/youtube/v3/search'
                params = {
                    'key': YOUTUBE_API_KEY,
                    'channelId': channel_id,
                    'part': 'snippet',
                    'order': 'date',
                    'maxResults': max_results,
                    'type': 'video'
                }
                
                async with session.get(url, params=params) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        videos = []
                        for item in data.get('items', []):
                            videos.append({
                                'id': item['id']['videoId'],
                                'title': item['snippet']['title'],
                                'description': item['snippet']['description'],
                                'published_at': item['snippet']['publishedAt'],
                                'thumbnail_url': item['snippet']['thumbnails']['high']['url'],
                                'url': f"https://youtube.com/watch?v={item['id']['videoId']}"
                            })
                        return videos
        except Exception as e:
            print(f"Error checking YouTube videos: {e}")
        return []
    
    async def check_youtube_live(self, channel_id: str) -> Optional[Dict]:
        """Verifica si un canal de YouTube está en vivo"""
        if not YOUTUBE_API_KEY:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f'https://www.googleapis.com/youtube/v3/search'
                params = {
                    'key': YOUTUBE_API_KEY,
                    'channelId': channel_id,
                    'part': 'snippet',
                    'eventType': 'live',
                    'type': 'video',
                    'maxResults': 1
                }
                
                async with session.get(url, params=params) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        if data.get('items'):
                            item = data['items'][0]
                            return {
                                'id': item['id']['videoId'],
                                'title': item['snippet']['title'],
                                'description': item['snippet']['description'],
                                'published_at': item['snippet']['publishedAt'],
                                'thumbnail_url': item['snippet']['thumbnails']['high']['url'],
                                'url': f"https://youtube.com/watch?v={item['id']['videoId']}"
                            }
        except Exception as e:
            print(f"Error checking YouTube live: {e}")
        return None
    
    def create_notification_embed(self, platform: str, content_type: str, data: Dict, 
                                 config: Dict, custom_message: str = None) -> discord.Embed:
        """Crea un embed para la notificación"""
        # Colores por plataforma
        platform_colors = {
            'twitch': 0x9146FF,
            'youtube': 0xFF0000,
            'tiktok': 0x000000,
            'instagram': 0xE4405F
        }
        
        color = int(config.get('embed_color', '#FF6B9D').replace('#', ''), 16)
        
        # Emojis por plataforma
        platform_emojis = {
            'twitch': '📺',
            'youtube': '🎥',
            'tiktok': '📱',
            'instagram': '📸'
        }
        
        # Emojis por tipo de contenido
        content_emojis = {
            'stream': '🔴',
            'video': '📹'
        }
        
        emoji = f"{platform_emojis.get(platform, '📢')}{content_emojis.get(content_type, '📢')}"
        
        embed = discord.Embed(
            title=f"{emoji} ¡Nuevo contenido de {data.get('channel_name', 'Canal')}!",
            description=custom_message or config.get('custom_message') or f"¡{data.get('channel_name', 'Canal')} acaba de publicar nuevo contenido!",
            color=color,
            url=data.get('url'),
            timestamp=datetime.utcnow()
        )
        
        # Agregar campos según el tipo de contenido
        if content_type == 'stream':
            if platform == 'twitch':
                embed.add_field(name="🎮 Juego", value=data.get('game_name', 'N/A'), inline=True)
                embed.add_field(name="👥 Espectadores", value=f"{data.get('viewer_count', 0):,}", inline=True)
            embed.add_field(name="📺 Plataforma", value=platform.title(), inline=True)
        elif content_type == 'video':
            embed.add_field(name="📅 Publicado", value=f"<t:{int(datetime.fromisoformat(data.get('published_at', '').replace('Z', '+00:00')).timestamp())}:R>", inline=True)
            embed.add_field(name="📺 Plataforma", value=platform.title(), inline=True)
        
        # Agregar thumbnail si está disponible
        if data.get('thumbnail_url'):
            embed.set_thumbnail(url=data.get('thumbnail_url'))
        
        # Footer
        embed.set_footer(text=f"FlozWer Notifications • {platform.title()}")
        
        return embed
    
    async def send_notification(self, guild_id: int, platform: str, content_type: str, data: Dict):
        """Envía una notificación al servidor"""
        config = self.get_server_config(guild_id)
        if not config or not config['enabled']:
            return
        
        try:
            guild = self.bot.get_guild(guild_id)
            if not guild:
                return
            
            channel = guild.get_channel(config['notification_channel_id'])
            if not channel:
                return
            
            # Crear embed
            embed = self.create_notification_embed(platform, content_type, data, config)
            
            # Preparar mensaje con pings
            ping_message = ""
            if config.get('ping_role_id'):
                role = guild.get_role(config['ping_role_id'])
                if role:
                    ping_message += f"{role.mention} "
            
            for user_id in config.get('ping_users', []):
                member = guild.get_member(user_id)
                if member:
                    ping_message += f"{member.mention} "
            
            # Enviar notificación
            message = await channel.send(content=ping_message.strip() if ping_message else None, embed=embed)
            
            # Auto-delete si está configurado
            if config.get('auto_delete_minutes', 0) > 0:
                await asyncio.sleep(config['auto_delete_minutes'] * 60)
                try:
                    await message.delete()
                except:
                    pass
            
            # Registrar en historial
            self.log_notification(guild_id, platform, data.get('channel_name', ''), 
                                content_type, data.get('id', ''), data.get('title', ''), 
                                data.get('url', ''), True)
            
        except Exception as e:
            print(f"Error sending notification: {e}")
    
    def log_notification(self, guild_id: int, platform: str, channel_name: str, 
                        content_type: str, content_id: str, content_title: str, 
                        content_url: str, sent: bool = False):
        """Registra una notificación en el historial"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT INTO notification_history 
            (guild_id, platform, channel_name, content_type, content_id, content_title, content_url, notification_sent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (guild_id, platform, channel_name, content_type, content_id, content_title, content_url, sent))
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error logging notification: {e}")
    
    @tasks.loop(minutes=5)
    async def check_streams(self):
        """Verifica streams en vivo cada 5 minutos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT DISTINCT guild_id, platform, channel_id, channel_name
            FROM monitored_channels 
            WHERE enabled = TRUE AND (notification_type = 'stream' OR notification_type = 'both')
            ''')
            
            channels = cursor.fetchall()
            conn.close()
            
            for guild_id, platform, channel_id, channel_name in channels:
                try:
                    if platform == 'twitch':
                        stream_data = await self.check_twitch_stream(channel_id)
                        if stream_data:
                            stream_data['channel_name'] = channel_name
                            await self.send_notification(guild_id, platform, 'stream', stream_data)
                    
                    elif platform == 'youtube':
                        live_data = await self.check_youtube_live(channel_id)
                        if live_data:
                            live_data['channel_name'] = channel_name
                            await self.send_notification(guild_id, platform, 'stream', live_data)
                
                except Exception as e:
                    print(f"Error checking stream for {channel_name}: {e}")
                    
        except Exception as e:
            print(f"Error in check_streams task: {e}")
    
    @tasks.loop(minutes=15)
    async def check_videos(self):
        """Verifica videos nuevos cada 15 minutos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT DISTINCT guild_id, platform, channel_id, channel_name, last_video_id
            FROM monitored_channels 
            WHERE enabled = TRUE AND (notification_type = 'video' OR notification_type = 'both')
            ''')
            
            channels = cursor.fetchall()
            conn.close()
            
            for guild_id, platform, channel_id, channel_name, last_video_id in channels:
                try:
                    if platform == 'youtube':
                        videos = await self.check_youtube_videos(channel_id, max_results=3)
                        
                        # Verificar si hay videos nuevos
                        new_videos = []
                        for video in videos:
                            if video['id'] != last_video_id:
                                new_videos.append(video)
                        
                        # Enviar notificaciones para videos nuevos
                        for video in new_videos:
                            video['channel_name'] = channel_name
                            await self.send_notification(guild_id, platform, 'video', video)
                        
                        # Actualizar último video ID
                        if new_videos:
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()
                            cursor.execute('''
                            UPDATE monitored_channels 
                            SET last_video_id = ? 
                            WHERE guild_id = ? AND platform = ? AND channel_id = ?
                            ''', (new_videos[0]['id'], guild_id, platform, channel_id))
                            conn.commit()
                            conn.close()
                
                except Exception as e:
                    print(f"Error checking videos for {channel_name}: {e}")
                    
        except Exception as e:
            print(f"Error in check_videos task: {e}")
    
    @check_streams.before_loop
    async def before_check_streams(self):
        """Espera hasta que el bot esté listo"""
        await self.bot.wait_until_ready()
    
    @check_videos.before_loop
    async def before_check_videos(self):
        """Espera hasta que el bot esté listo"""
        await self.bot.wait_until_ready()

# Comandos del sistema de notificaciones
class FlozwerNotificationCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.notifications = FlozwerNotifications(bot)
    
    @commands.command(name="flozwerconfig", help="Configura el sistema de notificaciones FlozWer")
    @commands.has_permissions(administrator=True)
    async def flozwer_config(self, ctx, option: str = None, *, value: str = None):
        """Configura el sistema de notificaciones"""
        if not option:
            # Mostrar configuración actual
            config = self.notifications.get_server_config(ctx.guild.id)
            if not config:
                embed = discord.Embed(
                    title="⚙️ Configuración FlozWer Notifications",
                    description="El sistema no está configurado. Usa `!flozwerconfig setup` para configurarlo.",
                    color=0xFF6B9D
                )
                await ctx.send(embed=embed)
                return
            
            embed = discord.Embed(
                title="⚙️ Configuración FlozWer Notifications",
                color=0xFF6B9D
            )
            
            channel = ctx.guild.get_channel(config['notification_channel_id'])
            embed.add_field(name="📺 Canal", value=channel.mention if channel else "No configurado", inline=True)
            embed.add_field(name="🔔 Estado", value="✅ Activado" if config['enabled'] else "❌ Desactivado", inline=True)
            
            if config.get('ping_role_id'):
                role = ctx.guild.get_role(config['ping_role_id'])
                embed.add_field(name="🎯 Rol", value=role.mention if role else "Rol no encontrado", inline=True)
            
            embed.add_field(name="🎨 Color", value=config.get('embed_color', '#FF6B9D'), inline=True)
            embed.add_field(name="⏰ Auto-delete", value=f"{config.get('auto_delete_minutes', 0)} minutos" if config.get('auto_delete_minutes', 0) > 0 else "Desactivado", inline=True)
            
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "setup":
            # Configuración inicial
            self.notifications.set_server_config(
                ctx.guild.id,
                notification_channel_id=ctx.channel.id,
                enabled=True
            )
            
            embed = discord.Embed(
                title="✅ Configuración Inicial Completada",
                description=f"El sistema de notificaciones FlozWer ha sido configurado en {ctx.channel.mention}",
                color=0x00FF00
            )
            embed.add_field(name="📋 Próximos pasos", value="1. Usa `!flozweradd` para agregar canales\n2. Usa `!flozwerconfig` para más opciones", inline=False)
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "channel" and value:
            # Configurar canal de notificaciones
            channel = discord.utils.get(ctx.guild.channels, name=value)
            if not channel:
                await ctx.send("❌ Canal no encontrado. Usa el nombre del canal.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                notification_channel_id=channel.id
            )
            
            embed = discord.Embed(
                title="✅ Canal Configurado",
                description=f"Las notificaciones se enviarán a {channel.mention}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "role" and value:
            # Configurar rol para pings
            role = discord.utils.get(ctx.guild.roles, name=value)
            if not role:
                await ctx.send("❌ Rol no encontrado. Usa el nombre del rol.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                ping_role_id=role.id
            )
            
            embed = discord.Embed(
                title="✅ Rol Configurado",
                description=f"Se hará ping a {role.mention} en las notificaciones",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "color" and value:
            # Configurar color del embed
            if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
                await ctx.send("❌ Color inválido. Usa formato hexadecimal (ej: #FF6B9D)")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                embed_color=value
            )
            
            embed = discord.Embed(
                title="✅ Color Configurado",
                description=f"Color de embeds cambiado a {value}",
                color=int(value.replace('#', ''), 16)
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "message" and value:
            # Configurar mensaje personalizado
            self.notifications.set_server_config(
                ctx.guild.id,
                custom_message=value
            )
            
            embed = discord.Embed(
                title="✅ Mensaje Personalizado",
                description=f"Mensaje configurado: {value}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "autodelete" and value:
            # Configurar auto-delete
            try:
                minutes = int(value)
                if minutes < 0 or minutes > 1440:  # Máximo 24 horas
                    await ctx.send("❌ Los minutos deben estar entre 0 y 1440")
                    return
            except ValueError:
                await ctx.send("❌ Valor inválido. Usa un número de minutos.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                auto_delete_minutes=minutes
            )
            
            embed = discord.Embed(
                title="✅ Auto-delete Configurado",
                description=f"Las notificaciones se eliminarán automáticamente después de {minutes} minutos" if minutes > 0 else "Auto-delete desactivado",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "enable":
            # Habilitar sistema
            self.notifications.set_server_config(
                ctx.guild.id,
                enabled=True
            )
            
            embed = discord.Embed(
                title="✅ Sistema Habilitado",
                description="El sistema de notificaciones FlozWer está ahora activo",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "disable":
            # Deshabilitar sistema
            self.notifications.set_server_config(
                ctx.guild.id,
                enabled=False
            )
            
            embed = discord.Embed(
                title="❌ Sistema Deshabilitado",
                description="El sistema de notificaciones FlozWer está ahora inactivo",
                color=0xFF0000
            )
            await ctx.send(embed=embed)
            return
        
        await ctx.send("❌ Opción no válida. Usa `!flozwerconfig` para ver las opciones disponibles.")
    
    @commands.command(name="flozweradd", help="Agrega un canal para monitorear")
    @commands.has_permissions(administrator=True)
    async def flozwer_add(self, ctx, platform: str, channel_url: str, notification_type: str = "both"):
        """Agrega un canal para monitorear"""
        platform = platform.lower()
        if platform not in ['twitch', 'youtube', 'tiktok', 'instagram']:
            await ctx.send("❌ Plataforma no soportada. Plataformas válidas: twitch, youtube, tiktok, instagram")
            return
        
        if notification_type not in ['stream', 'video', 'both']:
            await ctx.send("❌ Tipo de notificación inválido. Tipos válidos: stream, video, both")
            return
        
        # Extraer información del canal según la plataforma
        channel_info = await self.extract_channel_info(platform, channel_url)
        if not channel_info:
            await ctx.send("❌ No se pudo obtener información del canal. Verifica la URL.")
            return
        
        # Agregar canal
        success = self.notifications.add_monitored_channel(
            ctx.guild.id, platform, channel_info['id'], 
            channel_info['name'], channel_url, notification_type
        )
        
        if success:
            embed = discord.Embed(
                title="✅ Canal Agregado",
                description=f"**Canal:** {channel_info['name']}\n**Plataforma:** {platform.title()}\n**Tipo:** {notification_type}",
                color=0x00FF00
            )
            embed.add_field(name="🔗 URL", value=channel_url, inline=False)
            await ctx.send(embed=embed)
        else:
            await ctx.send("❌ Error al agregar el canal.")
    
    async def extract_channel_info(self, platform: str, url: str) -> Optional[Dict]:
        """Extrae información del canal desde la URL"""
        try:
            if platform == 'twitch':
                # Extraer nombre de usuario de Twitch
                match = re.search(r'twitch\.tv/([^/?]+)', url)
                if match:
                    username = match.group(1)
                    # Aquí necesitarías hacer una llamada a la API de Twitch para obtener el ID
                    # Por ahora, usamos el username como ID
                    return {
                        'id': username,
                        'name': username
                    }
            
            elif platform == 'youtube':
                # Extraer ID del canal de YouTube
                match = re.search(r'youtube\.com/(?:channel/|c/|user/)?([^/?]+)', url)
                if match:
                    channel_id = match.group(1)
                    return {
                        'id': channel_id,
                        'name': f"YouTube Channel ({channel_id})"
                    }
            
            # Para otras plataformas, usar el nombre como ID por ahora
            return {
                'id': url,
                'name': f"{platform.title()} Channel"
            }
            
        except Exception as e:
            print(f"Error extracting channel info: {e}")
            return None
    
    @commands.command(name="flozwerremove", help="Remueve un canal monitoreado")
    @commands.has_permissions(administrator=True)
    async def flozwer_remove(self, ctx, platform: str, channel_name: str):
        """Remueve un canal monitoreado"""
        platform = platform.lower()
        if platform not in ['twitch', 'youtube', 'tiktok', 'instagram']:
            await ctx.send("❌ Plataforma no soportada.")
            return
        
        # Buscar el canal
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        target_channel = None
        
        for channel in channels:
            if channel['platform'] == platform and channel['channel_name'].lower() == channel_name.lower():
                target_channel = channel
                break
        
        if not target_channel:
            await ctx.send("❌ Canal no encontrado.")
            return
        
        # Remover canal
        success = self.notifications.remove_monitored_channel(
            ctx.guild.id, platform, target_channel['channel_id']
        )
        
        if success:
            embed = discord.Embed(
                title="✅ Canal Removido",
                description=f"**Canal:** {target_channel['channel_name']}\n**Plataforma:** {platform.title()}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
        else:
            await ctx.send("❌ Error al remover el canal.")
    
    @commands.command(name="flozwerlist", help="Lista todos los canales monitoreados")
    async def flozwer_list(self, ctx):
        """Lista todos los canales monitoreados"""
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        
        if not channels:
            embed = discord.Embed(
                title="📋 Canales Monitoreados",
                description="No hay canales configurados.",
                color=0xFF6B9D
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="📋 Canales Monitoreados",
            color=0xFF6B9D
        )
        
        for channel in channels:
            status = "✅" if channel['enabled'] else "❌"
            embed.add_field(
                name=f"{status} {channel['channel_name']}",
                value=f"**Plataforma:** {channel['platform'].title()}\n**Tipo:** {channel['notification_type']}\n**URL:** {channel['channel_url']}",
                inline=False
            )
        
        await ctx.send(embed=embed)
    
    @commands.command(name="flozwerstatus", help="Muestra el estado del sistema de notificaciones")
    async def flozwer_status(self, ctx):
        """Muestra el estado del sistema"""
        config = self.notifications.get_server_config(ctx.guild.id)
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        
        embed = discord.Embed(
            title="📊 Estado FlozWer Notifications",
            color=0xFF6B9D
        )
        
        if config:
            embed.add_field(name="🔔 Sistema", value="✅ Activo" if config['enabled'] else "❌ Inactivo", inline=True)
            channel = ctx.guild.get_channel(config['notification_channel_id'])
            embed.add_field(name="📺 Canal", value=channel.mention if channel else "No configurado", inline=True)
            embed.add_field(name="📋 Canales", value=str(len(channels)), inline=True)
        else:
            embed.add_field(name="🔔 Sistema", value="❌ No configurado", inline=True)
        
        # Estadísticas de plataformas
        platforms = {}
        for channel in channels:
            platform = channel['platform']
            platforms[platform] = platforms.get(platform, 0) + 1
        
        if platforms:
            platform_stats = "\n".join([f"• {p.title()}: {c}" for p, c in platforms.items()])
            embed.add_field(name="📈 Plataformas", value=platform_stats, inline=False)
        
        await ctx.send(embed=embed)
    
    @commands.command(name="flozwertest", help="Envía una notificación de prueba")
    @commands.has_permissions(administrator=True)
    async def flozwer_test(self, ctx):
        """Envía una notificación de prueba"""
        config = self.notifications.get_server_config(ctx.guild.id)
        if not config:
            await ctx.send("❌ El sistema no está configurado.")
            return
        
        # Crear datos de prueba
        test_data = {
            'channel_name': 'Canal de Prueba',
            'title': 'Stream de Prueba',
            'url': 'https://example.com',
            'game_name': 'Juego de Prueba',
            'viewer_count': 1000
        }
        
        await self.notifications.send_notification(ctx.guild.id, 'twitch', 'stream', test_data)
        await ctx.send("✅ Notificación de prueba enviada.")
    
    @commands.command(name="flozwerhelp", help="Muestra ayuda del sistema FlozWer")
    async def flozwer_help(self, ctx):
        """Muestra ayuda del sistema"""
        embed = discord.Embed(
            title="📚 Ayuda FlozWer Notifications",
            description="Sistema de notificaciones para múltiples plataformas",
            color=0xFF6B9D
        )
        
        embed.add_field(
            name="⚙️ Configuración",
            value="""
            `!flozwerconfig setup` - Configuración inicial
            `!flozwerconfig channel <nombre>` - Cambiar canal
            `!flozwerconfig role <nombre>` - Configurar rol para pings
            `!flozwerconfig color <#hex>` - Cambiar color de embeds
            `!flozwerconfig message <texto>` - Mensaje personalizado
            `!flozwerconfig autodelete <minutos>` - Auto-eliminar notificaciones
            `!flozwerconfig enable/disable` - Activar/desactivar sistema
            """,
            inline=False
        )
        
        embed.add_field(
            name="📺 Gestión de Canales",
            value="""
            `!flozweradd <plataforma> <url> [tipo]` - Agregar canal
            `!flozwerremove <plataforma> <nombre>` - Remover canal
            `!flozwerlist` - Listar canales
            """,
            inline=False
        )
        
        embed.add_field(
            name="📊 Información",
            value="""
            `!flozwerstatus` - Estado del sistema
            `!flozwertest` - Enviar notificación de prueba
            `!flozwerhelp` - Esta ayuda
            """,
            inline=False
        )
        
        embed.add_field(
            name="🎯 Plataformas Soportadas",
            value="• Twitch (streams)\n• YouTube (videos y streams)\n• TikTok (videos)\n• Instagram (posts)",
            inline=False
        )
        
        embed.add_field(
            name="📋 Tipos de Notificación",
            value="• `stream` - Solo streams en vivo\n• `video` - Solo videos\n• `both` - Ambos (por defecto)",
            inline=False
        )
        
        embed.set_footer(text="FlozWer Notifications System")
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(FlozwerNotificationCommands(bot)) 