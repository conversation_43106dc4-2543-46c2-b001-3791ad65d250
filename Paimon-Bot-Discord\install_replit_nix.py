#!/usr/bin/env python3
"""
Instalador específico para Replit con entorno Nix
Configura el sistema de cifrado en Replit usando uv y pip
"""

import os
import sys
import subprocess
import importlib

def run_command(command):
    """Ejecutar comando y capturar salida"""
    try:
        print(f"🔧 Ejecutando: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.stdout:
            print(f"📤 Salida: {result.stdout.strip()}")
        if result.stderr and result.returncode != 0:
            print(f"⚠️ Error: {result.stderr.strip()}")
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"❌ Excepción: {e}")
        return False, "", str(e)

def check_package(package_name, import_name=None):
    """Verificar si un paquete está instalado"""
    if import_name is None:
        import_name = package_name

    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_with_uv():
    """Instalar usando uv (gestor de paquetes de Replit)"""
    print("📦 Intentando instalación con uv...")

    packages = ["cryptography>=41.0.0", "pycryptodome>=3.19.0"]

    for package in packages:
        print(f"⏳ Instalando {package} con uv...")
        success, stdout, stderr = run_command(f"uv add {package}")
        if success:
            print(f"✅ {package} instalado con uv")
        else:
            print(f"❌ Error con uv para {package}")
            return False

    return True

def install_with_pip_user():
    """Instalar usando pip --user"""
    print("📦 Intentando instalación con pip --user...")

    packages = ["cryptography>=41.0.0", "pycryptodome>=3.19.0"]

    for package in packages:
        print(f"⏳ Instalando {package} con pip --user...")
        success, stdout, stderr = run_command(f"pip install --user {package}")
        if success:
            print(f"✅ {package} instalado con pip --user")
        else:
            print(f"❌ Error con pip --user para {package}")
            return False

    return True

def install_with_pip_break():
    """Instalar usando pip --break-system-packages (último recurso)"""
    print("📦 Intentando instalación con pip --break-system-packages...")
    print("⚠️ ADVERTENCIA: Esto puede afectar el entorno del sistema")

    packages = ["cryptography>=41.0.0", "pycryptodome>=3.19.0"]

    for package in packages:
        print(f"⏳ Instalando {package} con pip --break-system-packages...")
        success, stdout, stderr = run_command(f"pip install --break-system-packages {package}")
        if success:
            print(f"✅ {package} instalado con pip --break-system-packages")
        else:
            print(f"❌ Error con pip --break-system-packages para {package}")
            return False

    return True

def try_sync_dependencies():
    """Intentar sincronizar dependencias del proyecto"""
    print("🔄 Intentando sincronizar dependencias del proyecto...")

    # Intentar con uv sync
    success, stdout, stderr = run_command("uv sync")
    if success:
        print("✅ Dependencias sincronizadas con uv sync")
        return True

    # Intentar con pip install -e .
    success, stdout, stderr = run_command("pip install -e .")
    if success:
        print("✅ Proyecto instalado con pip install -e .")
        return True

    return False

def setup_directories():
    """Crear directorios necesarios"""
    directories = ["data", "src"]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Directorio {directory}/ creado")
        else:
            print(f"📁 Directorio {directory}/ ya existe")

def main():
    print("🌐 Instalador para Replit con Entorno Nix - Paimon Bot")
    print("=" * 60)

    # Verificar entorno Replit
    if not any([os.getenv('REPL_ID'), os.getenv('REPLIT_DB_URL'), os.path.exists('/home/<USER>')]):
        print("⚠️ No se detectó entorno Replit")
        print("Este instalador está optimizado para Replit")

    # Verificar dependencias actuales
    print("\n📋 Verificando dependencias actuales...")
    dependencies = [
        ("cryptography", "cryptography"),
        ("pycryptodome", "Crypto"),
        ("flask", "flask")
    ]

    missing_packages = []
    for package, import_name in dependencies:
        if check_package(package, import_name):
            print(f"✅ {package} - Ya instalado")
        else:
            print(f"❌ {package} - No encontrado")
            missing_packages.append(package)

    if not missing_packages:
        print("\n🎉 ¡Todas las dependencias están instaladas!")
        setup_directories()
        print("✅ Sistema de cifrado listo para usar")
        return

    print(f"\n📦 Necesario instalar: {', '.join(missing_packages)}")

    # Intentar diferentes métodos de instalación
    installation_success = False

    # Método 1: Sincronizar dependencias del proyecto
    print("\n🔄 Método 1: Sincronizar dependencias del proyecto")
    if try_sync_dependencies():
        # Verificar si se instalaron
        if all(check_package(pkg, imp) for pkg, imp in dependencies if pkg in missing_packages):
            installation_success = True
            print("✅ Dependencias instaladas mediante sincronización")

    # Método 2: uv (gestor de Replit)
    if not installation_success:
        print("\n🔄 Método 2: Instalación con uv")
        if install_with_uv():
            # Verificar instalación
            if all(check_package(pkg, imp) for pkg, imp in dependencies if pkg in missing_packages):
                installation_success = True
                print("✅ Dependencias instaladas con uv")

    # Método 3: pip --user
    if not installation_success:
        print("\n🔄 Método 3: Instalación con pip --user")
        if install_with_pip_user():
            # Verificar instalación
            if all(check_package(pkg, imp) for pkg, imp in dependencies if pkg in missing_packages):
                installation_success = True
                print("✅ Dependencias instaladas con pip --user")

    # Método 4: pip --break-system-packages (último recurso)
    if not installation_success:
        print("\n🔄 Método 4: Instalación con pip --break-system-packages")
        print("⚠️ Este método puede afectar el entorno del sistema")

        user_input = input("¿Continuar? (s/N): ").lower().strip()
        if user_input in ['s', 'si', 'y', 'yes']:
            if install_with_pip_break():
                # Verificar instalación
                if all(check_package(pkg, imp) for pkg, imp in dependencies if pkg in missing_packages):
                    installation_success = True
                    print("✅ Dependencias instaladas con pip --break-system-packages")

    print("\n" + "=" * 60)

    if installation_success:
        print("🎉 ¡Instalación completada exitosamente!")
        setup_directories()

        print("\n🚀 Próximos pasos:")
        print("1. Reinicia el Repl (presiona 'Run')")
        print("2. Ejecuta: python verify_crypto_replit.py")
        print("3. Ejecuta: python main.py")

        print("\n🎯 Comandos disponibles:")
        print("• !register <usuario> <contraseña> - Registrar cuenta")
        print("• !login <contraseña> - Iniciar sesión")
        print("• !account - Ver estado de cuenta")

    else:
        print("❌ No se pudieron instalar todas las dependencias")

        print("\n🔧 Soluciones manuales:")
        print("1. En la consola de Replit:")
        print("   uv add cryptography pycryptodome")
        print("   uv sync")

        print("\n2. O intenta:")
        print("   pip install --user cryptography pycryptodome")

        print("\n3. Como último recurso:")
        print("   pip install --break-system-packages cryptography pycryptodome")

        print("\n4. Reinicia el Repl después de cualquier instalación")

if __name__ == "__main__":
    main()
