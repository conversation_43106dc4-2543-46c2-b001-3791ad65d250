# Bot de Discord Modular v2.0

## 🚀 Nueva Estructura Modular

El bot ha sido refactorizado para funcionar de forma modular, dividiendo las funcionalidades en archivos separados para mejor mantenimiento y organización.

## 📁 Estructura de Archivos

```
Dsbot2.0/
├── main.py                          # Archivo principal (REEMPLAZA DiscordBot2.py)
├── modules/                         # Carpeta de módulos
│   ├── __init__.py                  # Inicializador del paquete
│   ├── core.py                      # Configuración básica del bot
│   ├── database.py                  # Gestión de bases de datos
│   ├── leveling.py                  # Sistema de niveles y XP
│   ├── commands.py                  # Comandos básicos
│   ├── anti_raid.py                 # Sistema de protección
│   ├── ui_views.py                  # Interfaces de usuario
│   ├── music.py                     # Sistema de música (PENDIENTE)
│   ├── tts.py                       # Sistema TTS (PENDIENTE)
│   ├── stage.py                     # Sistema Stage (PENDIENTE)
│   └── github_integration.py        # Integración GitHub (PENDIENTE)
├── DiscordBot2.py                   # ARCHIVO ANTIGUO (Puedes eliminarlo)
└── [otros archivos existentes...]
```

## 🔄 Cómo Migrar

### 1. Eliminar el archivo antiguo
```bash
# Puedes eliminar o renombrar el archivo antiguo
mv DiscordBot2.py DiscordBot2_old.py
```

### 2. Usar el nuevo archivo principal
```bash
# Ejecutar el bot con la nueva estructura
python main.py
```

### 3. Configurar variables de entorno
Asegúrate de tener configuradas las variables de entorno:
- `TOKEN` - Token del bot de Discord
- `OPENAI_API_KEY` - Clave de API de OpenAI (opcional)
- `GITHUB_TOKEN` - Token de GitHub (opcional)

## 📦 Módulos Disponibles

### ✅ Completados
- **core.py** - Configuración básica, intents, bot instance
- **database.py** - Gestión de bases de datos SQLite
- **leveling.py** - Sistema de niveles, XP, leaderboard
- **commands.py** - Comandos básicos y slash commands
- **anti_raid.py** - Sistema de protección contra spam/raid
- **ui_views.py** - Interfaces de usuario y vistas

### ⏳ Pendientes (Puedes migrar desde DiscordBot2.py)
- **music.py** - Sistema de reproducción de música
- **tts.py** - Sistema de texto a voz
- **stage.py** - Sistema de gestión de niveles
- **github_integration.py** - Integración con GitHub

## 🎯 Ventajas de la Nueva Estructura

1. **Código más limpio** - Cada módulo tiene una responsabilidad específica
2. **Fácil mantenimiento** - Puedes editar un sistema sin afectar otros
3. **Mejor organización** - Fácil encontrar y modificar funcionalidades
4. **Escalabilidad** - Fácil agregar nuevos módulos
5. **Reutilización** - Los módulos pueden ser reutilizados en otros proyectos

## 🔧 Cómo Agregar Nuevos Módulos

1. **Crear el archivo** en la carpeta `modules/`
2. **Importar desde core.py** la instancia del bot
3. **Agregar la importación** en `main.py`
4. **Actualizar el __init__.py** si es necesario

### Ejemplo de nuevo módulo:
```python
# modules/ejemplo.py
from .core import bot

@bot.command(name="ejemplo")
async def ejemplo_command(ctx):
    await ctx.send("¡Hola desde el nuevo módulo!")
```

Luego en `main.py`:
```python
from modules.ejemplo import *
```

## 🚨 Comandos Importantes

### Sistema de Niveles
- `!top` - Muestra el top 10 del servidor
- `!levelroles` - Muestra roles por nivel
- `!setlevelrole` - Configura un rol por nivel
- `!resetlevel` - Reinicia nivel de usuario
- `!addpoints` - Agrega puntos a usuario

### Anti-raid
- `!antiraid` - Configura el sistema anti-raid
- `!clearwarnings` - Limpia advertencias de usuario
- `!raidstatus` - Muestra estado del sistema

### Comandos Básicos
- `!version` - Muestra versión del bot
- `!ping` - Verifica latencia
- `!com` - Lista todos los comandos
- `!restart` - Reinicia el bot (solo owner)
- `!off` - Apaga el bot (solo owner)

## 📝 Notas Importantes

1. **Bases de datos** - Se mantienen las mismas bases de datos
2. **Configuración** - Se mantiene la configuración existente
3. **Comandos** - Todos los comandos funcionan igual
4. **Eventos** - Los eventos se manejan en `main.py`

## 🔄 Migración Gradual

Si quieres migrar gradualmente:

1. **Mantén DiscordBot2.py** como respaldo
2. **Prueba main.py** en un servidor de desarrollo
3. **Migra módulos uno por uno** desde DiscordBot2.py
4. **Elimina DiscordBot2.py** cuando todo funcione

## 🆘 Solución de Problemas

### Error de importación
```bash
# Asegúrate de estar en el directorio correcto
cd Dsbot2.0
python main.py
```

### Módulo no encontrado
```bash
# Verifica que el archivo existe en modules/
ls modules/
```

### Base de datos no encontrada
```bash
# Las bases de datos se crean automáticamente
# Si hay problemas, elimina los archivos .db y reinicia
rm *.db
python main.py
```

## 🎉 ¡Listo!

Tu bot ahora tiene una estructura modular mucho más organizada y mantenible. ¡Disfruta del código más limpio! 