# 🎯 Paimon Counter - Sistema de Conteo Innovador

Un sistema de conteo completo y revolucionario para Discord con múltiples modos de juego, logros únicos y eventos especiales.

## ✨ Características Principales

### 🎮 **Múltiples Modos de Juego**
- **🔢 Modo Clásico:** Conteo tradicional (1, 2, 3...)
- **🧮 Modo Matemático:** Operaciones matemáticas (5+3=8)
- **📝 Modo Palabras:** Contar palabras en español
- **😊 Modo Emoji:** Contar emojis específicos
- **🎨 Modo Temático:** Contar elementos de temas específicos

### 🏆 **Sistema de Logros Únicos**
- **🥇 Primer Conteo** - Realizar el primer conteo
- **💯 Centenario** - Llegar al número 100
- **🧮 Matemático** - Usar operaciones matemáticas
- **⚡ Velocista** - Contar 3 veces en 30 segundos
- **🔄 Persistente** - Contar 10 veces sin errores
- **🛡️ Salvador** - Corregir un error de otro usuario
- **💎 Diamante** - 100+ conteos correctos

### 🎭 **Eventos Especiales**
- **⚡ Rush Hour** - Doble puntos por 10 minutos
- **🧮 Matemáticas Loco** - Solo operaciones complejas
- **🤫 Modo Silencioso** - Sin emojis ni texto extra
- **🎨 Desafío Temático** - Contar elementos específicos

### 📊 **Sistema de Estadísticas**
- Conteos totales y correctos
- Racha actual y mejor racha
- Puntos acumulados
- Logros desbloqueados
- Tabla de clasificación

## 🚀 Instalación

### 1. **Requisitos**
```bash
pip install discord.py
```

### 2. **Archivos Necesarios**
- `paimon_counter.py` - Sistema principal
- `counter_integration.py` - Ejemplo de integración

### 3. **Integración en tu Bot**

Agrega estas líneas a tu `DiscordBot2.py`:

```python
# Al inicio del archivo, después de los imports
from paimon_counter import CountingCommands

# En el evento on_ready, después de "print('Bot conectado como {bot.user}')"
async def setup_counting():
    await bot.add_cog(CountingCommands(bot))

# Agregar esta línea en on_ready:
await setup_counting()
```

## 📋 Comandos Disponibles

### 🔧 **Comandos de Administrador**
- `!setupcounter [modo] [tema]` - Configurar canal de conteo
- `!counterevent [tipo]` - Activar evento especial

### 📊 **Comandos de Usuario**
- `!counterstats [usuario]` - Ver estadísticas
- `!countertop` - Ver top de contadores
- `!counterhelp` - Mostrar ayuda

## 🎮 Cómo Jugar

### 1. **Configurar un Canal**
```
!setupcounter classic
!setupcounter math
!setupcounter words
!setupcounter emoji
!setupcounter theme gd_levels
```

### 2. **Modos de Juego**

#### 🔢 **Modo Clásico**
```
Usuario 1: 1
Usuario 2: 2
Usuario 3: 3
```

#### 🧮 **Modo Matemático**
```
Usuario 1: 1
Usuario 2: 2+1=3
Usuario 3: 4
Usuario 4: 2*3=6
```

#### 📝 **Modo Palabras**
```
Usuario 1: hola
Usuario 2: hola mundo
Usuario 3: hola mundo como
```

#### 😊 **Modo Emoji**
```
Usuario 1: 😀
Usuario 2: 😀😃
Usuario 3: 😀😃😄
```

#### 🎨 **Modo Temático**
```
Usuario 1: Stereo Madness
Usuario 2: Back on Track
Usuario 3: Polargeist
```

## 🏆 Sistema de Logros

### **Logros Automáticos**
- Se desbloquean automáticamente al cumplir condiciones
- Notificaciones especiales en el canal
- Progreso visible en estadísticas

### **Logros Disponibles**
- **🥇 Primer Conteo** - Primer conteo exitoso
- **💯 Centenario** - Llegar a 100 conteos
- **🧮 Matemático** - Usar operaciones matemáticas
- **⚡ Velocista** - Contar rápidamente
- **🔄 Persistente** - Racha de 10 conteos
- **🛡️ Salvador** - Corregir errores
- **💎 Diamante** - 100+ conteos correctos

## 🎉 Eventos Especiales

### **⚡ Rush Hour**
- Doble puntos por 10 minutos
- Activar con: `!counterevent rush`

### **🧮 Matemáticas Loco**
- Solo operaciones matemáticas complejas
- Activar con: `!counterevent math`

### **🤫 Modo Silencioso**
- Sin emojis ni texto extra
- Activar con: `!counterevent silent`

## 📊 Base de Datos

El sistema crea automáticamente:
- `paimon_counter.db` - Base de datos SQLite
- Tablas: `counting_channels`, `user_stats`, `counting_history`

## 🔧 Personalización

### **Temas Disponibles**
- `gd_levels` - Niveles de Geometry Dash
- `paimon` - Personajes de Genshin Impact
- `anime` - Personajes de anime
- `games` - Juegos populares

### **Agregar Nuevos Temas**
Edita la función `get_theme_items()` en `paimon_counter.py`:

```python
def get_theme_items(self, theme):
    themes = {
        'tu_tema': ['Elemento 1', 'Elemento 2', 'Elemento 3'],
        # ... más temas
    }
    return themes.get(theme, [])
```

## 🐛 Solución de Problemas

### **El botón no funciona**
- Verifica que el canal esté configurado con `!setupcounter`
- Asegúrate de que el bot tenga permisos en el canal

### **No se guardan estadísticas**
- Verifica que el bot tenga permisos de escritura
- Revisa que la base de datos se haya creado correctamente

### **Errores de sintaxis**
- Asegúrate de tener todas las dependencias instaladas
- Verifica que el código esté correctamente integrado

## 🎯 Características Únicas

### **Sistema de Rachas**
- Racha actual visible en cada conteo
- Mejor racha guardada permanentemente
- Logros basados en rachas

### **Puntos Dinámicos**
- Diferentes puntos según el modo
- Multiplicadores en eventos especiales
- Sistema de clasificación por puntos

### **Logros Progresivos**
- Logros que se desbloquean automáticamente
- Notificaciones especiales
- Progreso visible en estadísticas

## 🚀 Próximas Características

- [ ] Sistema de roles automáticos
- [ ] Más eventos especiales
- [ ] Integración con otros sistemas del bot
- [ ] Estadísticas globales entre servidores
- [ ] Modo competitivo por equipos

---

**¡Disfruta del sistema de conteo más innovador de Discord! 🎯** 