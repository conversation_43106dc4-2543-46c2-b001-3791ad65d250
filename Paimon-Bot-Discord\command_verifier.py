#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Sistema de Verificación Automática de Comandos - Paimon Bot
Verifica y mejora automáticamente todos los comandos del bot
"""

import re
import ast
import inspect
from typing import Dict, List, Tuple, Any
from datetime import datetime

class CommandVerifier:
    """Verificador automático de comandos del bot"""
    
    def __init__(self):
        self.commands_found = {}
        self.issues_found = []
        self.improvements_suggested = []
        self.github_sync_commands = []
        self.admin_commands = []
        self.critical_commands = []
        
    def analyze_bot_file(self, file_path: str = "Bot.py") -> Dict[str, Any]:
        """Analiza el archivo Bot.py y extrae información de todos los comandos"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Buscar todos los comandos
            command_pattern = r'@bot\.command\(name="([^"]+)"[^)]*\)\s*(?:@[^\n]*\s*)*async def ([^(]+)\([^)]*\):'
            commands = re.findall(command_pattern, content, re.MULTILINE)
            
            for cmd_name, func_name in commands:
                self.commands_found[cmd_name] = {
                    'function_name': func_name,
                    'has_admin_check': self._check_admin_permissions(content, func_name),
                    'has_error_handling': self._check_error_handling(content, func_name),
                    'has_github_sync': self._check_github_sync(content, func_name),
                    'category': self._categorize_command(cmd_name),
                    'line_number': self._find_command_line(content, cmd_name)
                }
            
            return {
                'total_commands': len(self.commands_found),
                'commands': self.commands_found,
                'issues': self.issues_found,
                'improvements': self.improvements_suggested
            }
            
        except Exception as e:
            self.issues_found.append(f"Error analizando archivo: {e}")
            return {}
    
    def _check_admin_permissions(self, content: str, func_name: str) -> bool:
        """Verifica si un comando tiene verificación de permisos de admin"""
        func_start = content.find(f"async def {func_name}")
        if func_start == -1:
            return False
        
        # Buscar la siguiente función para delimitar
        next_func = content.find("async def ", func_start + 1)
        func_content = content[func_start:next_func] if next_func != -1 else content[func_start:]
        
        admin_checks = [
            "@commands.has_permissions(administrator=True)",
            "check_admin_permissions",
            "@commands.is_owner()",
            "owner_server_only",
            "require_internal_role"
        ]
        
        return any(check in func_content for check in admin_checks)
    
    def _check_error_handling(self, content: str, func_name: str) -> bool:
        """Verifica si un comando tiene manejo de errores"""
        func_start = content.find(f"async def {func_name}")
        if func_start == -1:
            return False
        
        next_func = content.find("async def ", func_start + 1)
        func_content = content[func_start:next_func] if next_func != -1 else content[func_start:]
        
        return "try:" in func_content and "except" in func_content
    
    def _check_github_sync(self, content: str, func_name: str) -> bool:
        """Verifica si un comando sincroniza con GitHub"""
        func_start = content.find(f"async def {func_name}")
        if func_start == -1:
            return False
        
        next_func = content.find("async def ", func_start + 1)
        func_content = content[func_start:next_func] if next_func != -1 else content[func_start:]
        
        github_indicators = [
            "sync",
            "backup",
            "save_server_config",
            "auto_save",
            "github",
            "config_manager"
        ]
        
        return any(indicator in func_content.lower() for indicator in github_indicators)
    
    def _categorize_command(self, cmd_name: str) -> str:
        """Categoriza un comando según su nombre y funcionalidad"""
        categories = {
            'admin': ['restart', 'off', 'backup', 'sync', 'config', 'setversion', 'hotreload'],
            'xp_levels': ['rank', 'top', 'levelroles', 'setlevelrole', 'addpoints', 'resetxp'],
            'casino': ['casino', 'blackjack', 'crash', 'balance', 'daily', 'marketplace'],
            'music': ['playmusic', 'skipmusic', 'stopmusic', 'queuemusic', 'volumemusic'],
            'tts': ['paimontts', 'stoptts', 'ttsstatus'],
            'stage': ['stage', 'Stage', 'showstage', 'request', 'profile', 'queue'],
            'security': ['register', 'login', 'logout', 'changepass', 'deleteaccount'],
            'channels': ['autocomenzar', 'editcanales', 'limpiarcanales', 'aicreate'],
            'utility': ['ping', 'version', 'com', 'help', 'memory', 'cleanup'],
            'counter': ['setupcounter', 'counterstats', 'countertop', 'counterhelp'],
            'language': ['idioma', 'lang', 'detectidioma', 'autoidioma']
        }
        
        for category, commands in categories.items():
            if cmd_name in commands:
                return category
        
        return 'other'
    
    def _find_command_line(self, content: str, cmd_name: str) -> int:
        """Encuentra el número de línea donde está definido un comando"""
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if f'@bot.command(name="{cmd_name}"' in line:
                return i + 1
        return -1
    
    def generate_improvement_report(self) -> str:
        """Genera un reporte completo de mejoras sugeridas"""
        report = []
        report.append("🔍 REPORTE DE VERIFICACIÓN DE COMANDOS")
        report.append("=" * 60)
        report.append(f"📊 Total de comandos analizados: {len(self.commands_found)}")
        report.append(f"⚠️ Problemas encontrados: {len(self.issues_found)}")
        report.append(f"💡 Mejoras sugeridas: {len(self.improvements_suggested)}")
        report.append("")
        
        # Categorizar comandos
        categories = {}
        for cmd_name, cmd_info in self.commands_found.items():
            category = cmd_info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(cmd_name)
        
        report.append("📋 COMANDOS POR CATEGORÍA:")
        report.append("-" * 40)
        for category, commands in categories.items():
            report.append(f"{category.upper()}: {len(commands)} comandos")
            for cmd in commands[:5]:  # Mostrar solo los primeros 5
                cmd_info = self.commands_found[cmd]
                status = "✅" if cmd_info['has_error_handling'] else "⚠️"
                report.append(f"  {status} !{cmd}")
            if len(commands) > 5:
                report.append(f"  ... y {len(commands) - 5} más")
            report.append("")
        
        # Comandos que necesitan mejoras
        needs_improvement = []
        for cmd_name, cmd_info in self.commands_found.items():
            issues = []
            if not cmd_info['has_error_handling']:
                issues.append("Sin manejo de errores")
            if cmd_info['category'] in ['admin', 'xp_levels', 'casino'] and not cmd_info['has_github_sync']:
                issues.append("Sin sincronización GitHub")
            if cmd_info['category'] == 'admin' and not cmd_info['has_admin_check']:
                issues.append("Sin verificación de permisos")
            
            if issues:
                needs_improvement.append((cmd_name, issues))
        
        if needs_improvement:
            report.append("🔧 COMANDOS QUE NECESITAN MEJORAS:")
            report.append("-" * 40)
            for cmd_name, issues in needs_improvement[:10]:  # Top 10
                report.append(f"!{cmd_name}:")
                for issue in issues:
                    report.append(f"  • {issue}")
                report.append("")
        
        return "\n".join(report)
    
    def get_critical_commands_status(self) -> Dict[str, bool]:
        """Obtiene el estado de comandos críticos"""
        critical_commands = [
            'sync', 'backup', 'config', 'restart', 'off',
            'rank', 'top', 'addpoints', 'resetxp',
            'casino', 'balance', 'daily',
            'register', 'login', 'logout'
        ]
        
        status = {}
        for cmd in critical_commands:
            if cmd in self.commands_found:
                cmd_info = self.commands_found[cmd]
                status[cmd] = {
                    'exists': True,
                    'has_error_handling': cmd_info['has_error_handling'],
                    'has_admin_check': cmd_info['has_admin_check'],
                    'has_github_sync': cmd_info['has_github_sync']
                }
            else:
                status[cmd] = {'exists': False}
        
        return status

def main():
    """Función principal para ejecutar la verificación"""
    verifier = CommandVerifier()
    results = verifier.analyze_bot_file()
    
    print(verifier.generate_improvement_report())
    
    # Guardar reporte en archivo
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"command_verification_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(verifier.generate_improvement_report())
    
    print(f"\n📄 Reporte guardado en: {report_file}")
    
    return results

if __name__ == "__main__":
    main()
