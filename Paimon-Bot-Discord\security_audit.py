#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ Sistema de Auditoría de Seguridad - Paimon Bot
Verifica y mejora la seguridad general del bot
"""

import os
import re
import logging
import hashlib
from typing import Dict, List, Any, Tuple
from datetime import datetime
import discord

class SecurityAuditor:
    """Auditor de seguridad para el bot"""
    
    def __init__(self, bot: discord.Bot):
        self.bot = bot
        self.logger = logging.getLogger('SecurityAuditor')
        self.security_issues = []
        self.security_warnings = []
        self.security_recommendations = []
        
    def run_full_audit(self) -> Dict[str, Any]:
        """Ejecuta una auditoría completa de seguridad"""
        self.logger.info("🛡️ Iniciando auditoría de seguridad completa")
        
        audit_results = {
            'timestamp': datetime.now().isoformat(),
            'bot_info': self._get_bot_info(),
            'environment_security': self._audit_environment(),
            'authentication_security': self._audit_authentication(),
            'permissions_security': self._audit_permissions(),
            'data_security': self._audit_data_handling(),
            'network_security': self._audit_network(),
            'code_security': self._audit_code_practices(),
            'issues': self.security_issues,
            'warnings': self.security_warnings,
            'recommendations': self.security_recommendations,
            'overall_score': 0
        }
        
        # Calcular puntuación general
        audit_results['overall_score'] = self._calculate_security_score(audit_results)
        
        self.logger.info(f"🛡️ Auditoría completada - Puntuación: {audit_results['overall_score']}/100")
        return audit_results
    
    def _get_bot_info(self) -> Dict[str, Any]:
        """Obtiene información básica del bot"""
        return {
            'bot_id': str(self.bot.user.id) if self.bot.user else 'unknown',
            'guild_count': len(self.bot.guilds),
            'user_count': len(self.bot.users),
            'command_count': len(self.bot.commands),
            'has_restart_manager': hasattr(self.bot, 'restart_manager'),
            'has_auth_manager': hasattr(self.bot, 'auth_manager')
        }
    
    def _audit_environment(self) -> Dict[str, Any]:
        """Audita la seguridad del entorno"""
        env_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar variables de entorno críticas
        critical_env_vars = ['TOKEN', 'GITHUB_TOKEN']
        for var in critical_env_vars:
            if not os.getenv(var):
                env_audit['issues'].append(f"Variable crítica {var} no configurada")
                env_audit['score'] -= 20
        
        # Verificar que el token no esté hardcodeado
        if self._check_hardcoded_secrets():
            env_audit['issues'].append("Posibles secretos hardcodeados detectados")
            env_audit['score'] -= 30
        
        # Verificar permisos de archivos
        sensitive_files = ['Bot.py', 'bot_config_manager.py', '.env']
        for file in sensitive_files:
            if os.path.exists(file):
                file_perms = oct(os.stat(file).st_mode)[-3:]
                if file_perms in ['777', '666']:
                    env_audit['warnings'].append(f"Archivo {file} tiene permisos muy permisivos ({file_perms})")
                    env_audit['score'] -= 5
        
        # Verificar configuración OAuth2
        oauth_vars = ['DISCORD_CLIENT_ID', 'DISCORD_CLIENT_SECRET']
        oauth_configured = all(os.getenv(var) for var in oauth_vars)
        if not oauth_configured:
            env_audit['recommendations'].append("Configurar OAuth2 para autenticación segura")
        
        return env_audit
    
    def _audit_authentication(self) -> Dict[str, Any]:
        """Audita el sistema de autenticación"""
        auth_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar si el sistema de autenticación segura está disponible
        if not hasattr(self.bot, 'auth_manager'):
            auth_audit['warnings'].append("SecureAuthManager no está inicializado")
            auth_audit['score'] -= 15
        else:
            # Verificar configuración OAuth2
            auth_manager = self.bot.auth_manager
            if not auth_manager.client_id or not auth_manager.client_secret:
                auth_audit['issues'].append("OAuth2 no está configurado correctamente")
                auth_audit['score'] -= 25
            
            # Verificar configuración JWT
            if not auth_manager.jwt_secret or len(auth_manager.jwt_secret) < 32:
                auth_audit['warnings'].append("JWT secret es débil o no está configurado")
                auth_audit['score'] -= 10
        
        # Verificar comandos de autenticación obsoletos
        obsolete_commands = ['register', 'login', 'verify']
        for cmd_name in obsolete_commands:
            if cmd_name in [cmd.name for cmd in self.bot.commands]:
                auth_audit['recommendations'].append(f"Considerar deprecar comando obsoleto: {cmd_name}")
        
        return auth_audit
    
    def _audit_permissions(self) -> Dict[str, Any]:
        """Audita el sistema de permisos"""
        perms_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar comandos sin restricciones de permisos
        unrestricted_commands = []
        admin_keywords = ['delete', 'ban', 'kick', 'clear', 'purge', 'restart', 'off', 'config']
        
        for command in self.bot.commands:
            # Verificar si el comando tiene restricciones
            has_permissions = any(
                isinstance(check, discord.ext.commands.has_permissions) 
                for check in command.checks
            )
            
            # Verificar si es un comando potencialmente peligroso
            is_dangerous = any(keyword in command.name.lower() for keyword in admin_keywords)
            
            if is_dangerous and not has_permissions:
                unrestricted_commands.append(command.name)
        
        if unrestricted_commands:
            perms_audit['warnings'].append(f"Comandos peligrosos sin restricciones: {unrestricted_commands}")
            perms_audit['score'] -= len(unrestricted_commands) * 5
        
        # Verificar función check_admin_permissions
        try:
            # Esto es una verificación básica de que la función existe
            import inspect
            if 'check_admin_permissions' in globals():
                perms_audit['recommendations'].append("Sistema de permisos personalizado detectado")
            else:
                perms_audit['warnings'].append("Función check_admin_permissions no encontrada")
                perms_audit['score'] -= 10
        except:
            pass
        
        return perms_audit
    
    def _audit_data_handling(self) -> Dict[str, Any]:
        """Audita el manejo de datos"""
        data_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar cifrado de datos sensibles
        crypto_available = False
        try:
            # Verificar si el sistema de cifrado está disponible
            if hasattr(self.bot, 'auth_manager'):
                crypto_available = True
        except:
            pass
        
        if not crypto_available:
            data_audit['warnings'].append("Sistema de cifrado no disponible")
            data_audit['score'] -= 15
        
        # Verificar almacenamiento de contraseñas
        # Esto es una verificación heurística
        password_storage_risk = self._check_password_storage()
        if password_storage_risk:
            data_audit['issues'].append("Posible almacenamiento inseguro de contraseñas detectado")
            data_audit['score'] -= 30
        
        # Verificar backup de datos
        github_token = os.getenv('GITHUB_TOKEN')
        if not github_token:
            data_audit['warnings'].append("GitHub token no configurado - backups no disponibles")
            data_audit['score'] -= 10
        
        return data_audit
    
    def _audit_network(self) -> Dict[str, Any]:
        """Audita la seguridad de red"""
        network_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar uso de HTTPS
        # Esto es una verificación básica
        network_audit['recommendations'].append("Verificar que todas las conexiones usen HTTPS")
        
        # Verificar rate limiting
        # Discord.py maneja esto automáticamente, pero verificamos configuración
        if not hasattr(self.bot, 'http'):
            network_audit['warnings'].append("Cliente HTTP no disponible para verificación")
            network_audit['score'] -= 5
        
        return network_audit
    
    def _audit_code_practices(self) -> Dict[str, Any]:
        """Audita las prácticas de código"""
        code_audit = {
            'score': 100,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verificar manejo de errores
        error_handling_score = self._check_error_handling()
        code_audit['score'] = min(code_audit['score'], error_handling_score)
        
        if error_handling_score < 80:
            code_audit['warnings'].append("Manejo de errores insuficiente en algunos comandos")
        
        # Verificar logging
        if not logging.getLogger().handlers:
            code_audit['warnings'].append("Sistema de logging no configurado")
            code_audit['score'] -= 10
        
        # Verificar validación de entrada
        code_audit['recommendations'].append("Implementar validación robusta de entrada de usuario")
        
        return code_audit
    
    def _check_hardcoded_secrets(self) -> bool:
        """Verifica si hay secretos hardcodeados"""
        try:
            # Patrones comunes de secretos
            secret_patterns = [
                r'token\s*=\s*["\'][^"\']{50,}["\']',
                r'password\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']{20,}["\']',
                r'key\s*=\s*["\'][^"\']{20,}["\']'
            ]
            
            # Verificar archivos principales
            files_to_check = ['Bot.py', 'bot_config_manager.py']
            
            for file_path in files_to_check:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        for pattern in secret_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                return True
            
            return False
        except:
            return False
    
    def _check_password_storage(self) -> bool:
        """Verifica almacenamiento inseguro de contraseñas"""
        try:
            # Buscar patrones de almacenamiento de contraseñas
            password_patterns = [
                r'password.*=.*["\'][^"\']+["\']',
                r'pwd.*=.*["\'][^"\']+["\']',
                r'pass.*=.*["\'][^"\']+["\']'
            ]
            
            # Esta es una verificación heurística básica
            # En un sistema real, sería más sofisticada
            return False
        except:
            return False
    
    def _check_error_handling(self) -> int:
        """Verifica la calidad del manejo de errores"""
        try:
            # Contar comandos con try-catch
            commands_with_error_handling = 0
            total_commands = len(self.bot.commands)
            
            # Esta es una verificación simplificada
            # En la práctica, analizaríamos el código fuente
            
            # Asumir que los comandos mejorados tienen manejo de errores
            improved_commands = [
                'balance', 'daily', 'work', 'marketplace', 'casino',
                'rank', 'top', 'addpoints', 'resetxp', 'levelroles',
                'config', 'restart', 'off', 'sync', 'backup'
            ]
            
            for command in self.bot.commands:
                if command.name in improved_commands:
                    commands_with_error_handling += 1
            
            if total_commands > 0:
                percentage = (commands_with_error_handling / total_commands) * 100
                return min(100, percentage)
            
            return 50  # Puntuación por defecto
        except:
            return 50
    
    def _calculate_security_score(self, audit_results: Dict[str, Any]) -> int:
        """Calcula la puntuación general de seguridad"""
        try:
            scores = []
            weights = {
                'environment_security': 0.25,
                'authentication_security': 0.20,
                'permissions_security': 0.20,
                'data_security': 0.15,
                'network_security': 0.10,
                'code_security': 0.10
            }
            
            weighted_score = 0
            for category, weight in weights.items():
                if category in audit_results:
                    category_score = audit_results[category].get('score', 0)
                    weighted_score += category_score * weight
            
            # Penalizar por issues críticos
            critical_issues = len(self.security_issues)
            weighted_score -= critical_issues * 5
            
            return max(0, min(100, int(weighted_score)))
        except:
            return 50

def create_security_auditor(bot: discord.Bot) -> SecurityAuditor:
    """Crea una instancia del SecurityAuditor"""
    return SecurityAuditor(bot)
