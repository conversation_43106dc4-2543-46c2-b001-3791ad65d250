#!/usr/bin/env python3
"""
Optimizador de Comandos del Bot Paimon
Analiza y mejora todos los comandos para reducir errores
"""

import re
import ast
import os
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class CommandIssue:
    """Representa un problema encontrado en un comando"""
    command_name: str
    issue_type: str
    severity: str  # 'critical', 'high', 'medium', 'low'
    description: str
    line_number: int
    suggested_fix: str

@dataclass
class CommandAnalysis:
    """Análisis completo de un comando"""
    name: str
    function_name: str
    line_number: int
    has_error_handling: bool
    has_parameter_validation: bool
    has_permission_check: bool
    has_rate_limiting: bool
    has_logging: bool
    issues: List[CommandIssue]
    complexity_score: int

class CommandOptimizer:
    """Optimizador principal de comandos"""
    
    def __init__(self, bot_file: str = "Paimon-Bot-Discord/Bot.py"):
        self.bot_file = bot_file
        self.commands_analysis: Dict[str, CommandAnalysis] = {}
        self.critical_issues: List[CommandIssue] = []
        self.improvement_suggestions: List[str] = []
        
    def analyze_all_commands(self) -> Dict[str, any]:
        """Analiza todos los comandos del bot"""
        print("🔍 Iniciando análisis completo de comandos...")
        
        if not os.path.exists(self.bot_file):
            print(f"❌ Archivo {self.bot_file} no encontrado")
            return {}
        
        with open(self.bot_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Encontrar todos los comandos
        commands = self._extract_commands(content)
        print(f"📊 Encontrados {len(commands)} comandos")
        
        # Analizar cada comando
        for cmd_name, cmd_info in commands.items():
            analysis = self._analyze_command(content, cmd_name, cmd_info)
            self.commands_analysis[cmd_name] = analysis
            
            # Recopilar issues críticos
            for issue in analysis.issues:
                if issue.severity in ['critical', 'high']:
                    self.critical_issues.append(issue)
        
        return self._generate_report()
    
    def _extract_commands(self, content: str) -> Dict[str, Dict]:
        """Extrae todos los comandos del archivo"""
        commands = {}
        
        # Patrón para comandos con decorador @bot.command
        pattern = r'@bot\.command\(name="([^"]+)"[^)]*\)\s*(?:@[^\n]*\s*)*async def ([^(]+)\([^)]*\):'
        matches = re.finditer(pattern, content, re.MULTILINE)
        
        for match in matches:
            cmd_name = match.group(1)
            func_name = match.group(2)
            line_num = content[:match.start()].count('\n') + 1
            
            commands[cmd_name] = {
                'function_name': func_name,
                'line_number': line_num,
                'match_start': match.start(),
                'match_end': match.end()
            }
        
        return commands
    
    def _analyze_command(self, content: str, cmd_name: str, cmd_info: Dict) -> CommandAnalysis:
        """Analiza un comando específico"""
        issues = []
        
        # Extraer el código del comando
        func_code = self._extract_function_code(content, cmd_info)
        
        # Análisis de manejo de errores
        has_error_handling = self._check_error_handling(func_code)
        if not has_error_handling:
            issues.append(CommandIssue(
                command_name=cmd_name,
                issue_type="missing_error_handling",
                severity="high",
                description="El comando no tiene manejo de errores adecuado",
                line_number=cmd_info['line_number'],
                suggested_fix="Agregar try-except con logging apropiado"
            ))
        
        # Análisis de validación de parámetros
        has_param_validation = self._check_parameter_validation(func_code)
        if not has_param_validation:
            issues.append(CommandIssue(
                command_name=cmd_name,
                issue_type="missing_parameter_validation",
                severity="medium",
                description="El comando no valida parámetros de entrada",
                line_number=cmd_info['line_number'],
                suggested_fix="Agregar validación de parámetros antes de procesarlos"
            ))
        
        # Análisis de permisos
        has_permission_check = self._check_permissions(func_code, content, cmd_info)
        
        # Análisis de rate limiting
        has_rate_limiting = self._check_rate_limiting(func_code)
        
        # Análisis de logging
        has_logging = self._check_logging(func_code)
        
        # Calcular score de complejidad
        complexity_score = self._calculate_complexity(func_code)
        
        # Issues específicos por tipo de comando
        self._check_command_specific_issues(cmd_name, func_code, issues)
        
        return CommandAnalysis(
            name=cmd_name,
            function_name=cmd_info['function_name'],
            line_number=cmd_info['line_number'],
            has_error_handling=has_error_handling,
            has_parameter_validation=has_param_validation,
            has_permission_check=has_permission_check,
            has_rate_limiting=has_rate_limiting,
            has_logging=has_logging,
            issues=issues,
            complexity_score=complexity_score
        )
    
    def _extract_function_code(self, content: str, cmd_info: Dict) -> str:
        """Extrae el código completo de una función"""
        lines = content.split('\n')
        start_line = cmd_info['line_number'] - 1
        
        # Encontrar el final de la función
        indent_level = None
        end_line = len(lines)
        
        for i in range(start_line, len(lines)):
            line = lines[i]
            if line.strip() == "":
                continue
                
            current_indent = len(line) - len(line.lstrip())
            
            if indent_level is None and line.strip().startswith('async def'):
                indent_level = current_indent
                continue
            
            if indent_level is not None and line.strip() and current_indent <= indent_level:
                if not line.strip().startswith('@') and not line.strip().startswith('async def'):
                    end_line = i
                    break
        
        return '\n'.join(lines[start_line:end_line])
    
    def _check_error_handling(self, func_code: str) -> bool:
        """Verifica si el comando tiene manejo de errores"""
        return 'try:' in func_code and 'except' in func_code
    
    def _check_parameter_validation(self, func_code: str) -> bool:
        """Verifica si el comando valida parámetros"""
        validation_patterns = [
            r'if\s+not\s+\w+',
            r'if\s+\w+\s+is\s+None',
            r'if\s+len\(',
            r'isinstance\(',
            r'\.isdigit\(',
            r'validate_'
        ]
        
        for pattern in validation_patterns:
            if re.search(pattern, func_code):
                return True
        return False
    
    def _check_permissions(self, func_code: str, content: str, cmd_info: Dict) -> bool:
        """Verifica si el comando tiene verificación de permisos"""
        # Buscar decoradores de permisos
        start_pos = max(0, cmd_info['match_start'] - 500)
        end_pos = cmd_info['match_end']
        context = content[start_pos:end_pos]
        
        permission_patterns = [
            r'@commands\.has_permissions',
            r'@commands\.has_role',
            r'@commands\.is_owner',
            r'has_permissions',
            r'check_admin',
            r'is_admin'
        ]
        
        for pattern in permission_patterns:
            if re.search(pattern, context) or re.search(pattern, func_code):
                return True
        return False
    
    def _check_rate_limiting(self, func_code: str) -> bool:
        """Verifica si el comando tiene rate limiting"""
        rate_limit_patterns = [
            r'cooldown',
            r'rate_limit',
            r'last_used',
            r'time\.time\(\)',
            r'datetime\.now\(\)'
        ]
        
        for pattern in rate_limit_patterns:
            if re.search(pattern, func_code):
                return True
        return False
    
    def _check_logging(self, func_code: str) -> bool:
        """Verifica si el comando tiene logging"""
        logging_patterns = [
            r'print\(',
            r'logging\.',
            r'logger\.',
            r'log_'
        ]
        
        for pattern in logging_patterns:
            if re.search(pattern, func_code):
                return True
        return False
    
    def _calculate_complexity(self, func_code: str) -> int:
        """Calcula un score de complejidad del comando"""
        complexity = 0
        
        # Contar estructuras de control
        complexity += len(re.findall(r'\bif\b', func_code)) * 2
        complexity += len(re.findall(r'\bfor\b', func_code)) * 3
        complexity += len(re.findall(r'\bwhile\b', func_code)) * 3
        complexity += len(re.findall(r'\btry\b', func_code)) * 2
        complexity += len(re.findall(r'\bawait\b', func_code)) * 1
        
        # Contar líneas de código
        lines = [line for line in func_code.split('\n') if line.strip() and not line.strip().startswith('#')]
        complexity += len(lines)
        
        return complexity
    
    def _check_command_specific_issues(self, cmd_name: str, func_code: str, issues: List[CommandIssue]):
        """Verifica issues específicos según el tipo de comando"""
        
        # Comandos de economía
        if cmd_name in ['balance', 'daily', 'work', 'casino', 'marketplace', 'give', 'rob']:
            if 'user_data' in func_code and 'save_user_data' not in func_code:
                issues.append(CommandIssue(
                    command_name=cmd_name,
                    issue_type="missing_data_persistence",
                    severity="critical",
                    description="Comando de economía no guarda datos del usuario",
                    line_number=0,
                    suggested_fix="Agregar save_user_data() después de modificar datos"
                ))
        
        # Comandos de administración
        if cmd_name in ['restart', 'off', 'sync', 'backup']:
            if not self._check_permissions(func_code, "", {"match_start": 0, "match_end": 0}):
                issues.append(CommandIssue(
                    command_name=cmd_name,
                    issue_type="missing_admin_check",
                    severity="critical",
                    description="Comando administrativo sin verificación de permisos",
                    line_number=0,
                    suggested_fix="Agregar @commands.has_permissions(administrator=True)"
                ))
    
    def _generate_report(self) -> Dict[str, any]:
        """Genera reporte completo del análisis"""
        total_commands = len(self.commands_analysis)
        commands_with_issues = len([cmd for cmd in self.commands_analysis.values() if cmd.issues])
        critical_issues_count = len([issue for issue in self.critical_issues if issue.severity == 'critical'])
        high_issues_count = len([issue for issue in self.critical_issues if issue.severity == 'high'])
        
        # Categorizar comandos por calidad
        excellent_commands = []
        good_commands = []
        needs_improvement = []
        critical_commands = []
        
        for cmd_name, analysis in self.commands_analysis.items():
            critical_issues = [i for i in analysis.issues if i.severity == 'critical']
            high_issues = [i for i in analysis.issues if i.severity == 'high']
            
            if critical_issues:
                critical_commands.append(cmd_name)
            elif high_issues or len(analysis.issues) > 3:
                needs_improvement.append(cmd_name)
            elif analysis.issues:
                good_commands.append(cmd_name)
            else:
                excellent_commands.append(cmd_name)
        
        return {
            'summary': {
                'total_commands': total_commands,
                'commands_with_issues': commands_with_issues,
                'critical_issues': critical_issues_count,
                'high_issues': high_issues_count,
                'health_score': max(0, 100 - (critical_issues_count * 10 + high_issues_count * 5))
            },
            'categories': {
                'excellent': excellent_commands,
                'good': good_commands,
                'needs_improvement': needs_improvement,
                'critical': critical_commands
            },
            'detailed_analysis': self.commands_analysis,
            'critical_issues': self.critical_issues
        }
    
    def generate_fixes(self) -> List[str]:
        """Genera lista de fixes recomendados"""
        fixes = []
        
        for issue in self.critical_issues:
            fixes.append(f"🔴 {issue.command_name}: {issue.description} - {issue.suggested_fix}")
        
        return fixes

def main():
    """Función principal"""
    print("🚀 OPTIMIZADOR DE COMANDOS PAIMON BOT")
    print("=" * 50)
    
    optimizer = CommandOptimizer()
    report = optimizer.analyze_all_commands()
    
    if not report:
        print("❌ No se pudo generar el reporte")
        return
    
    # Mostrar resumen
    summary = report['summary']
    print(f"\n📊 RESUMEN DEL ANÁLISIS:")
    print(f"   • Total de comandos: {summary['total_commands']}")
    print(f"   • Comandos con issues: {summary['commands_with_issues']}")
    print(f"   • Issues críticos: {summary['critical_issues']}")
    print(f"   • Issues altos: {summary['high_issues']}")
    print(f"   • Score de salud: {summary['health_score']}/100")
    
    # Mostrar categorías
    categories = report['categories']
    print(f"\n🎯 CATEGORÍAS DE COMANDOS:")
    print(f"   ✅ Excelentes: {len(categories['excellent'])} comandos")
    print(f"   🟡 Buenos: {len(categories['good'])} comandos")
    print(f"   🟠 Necesitan mejora: {len(categories['needs_improvement'])} comandos")
    print(f"   🔴 Críticos: {len(categories['critical'])} comandos")
    
    # Mostrar comandos críticos
    if categories['critical']:
        print(f"\n🚨 COMANDOS CRÍTICOS QUE REQUIEREN ATENCIÓN INMEDIATA:")
        for cmd in categories['critical']:
            print(f"   • !{cmd}")
    
    # Generar fixes
    fixes = optimizer.generate_fixes()
    if fixes:
        print(f"\n🔧 FIXES RECOMENDADOS:")
        for fix in fixes[:10]:  # Mostrar solo los primeros 10
            print(f"   {fix}")
    
    # Guardar reporte detallado
    with open('command_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 Reporte detallado guardado en: command_analysis_report.json")
    
    return report

if __name__ == "__main__":
    main()
