# 🔴 FlozWer Notifications

Sistema completo de notificaciones para múltiples plataformas de streaming y contenido, integrado en tu bot de Discord.

## 🎯 Características

### 📺 Plataformas Soportadas
- **Twitch** - Streams en vivo
- **YouTube** - Videos y streams en vivo
- **TikTok** - Videos (próximamente)
- **Instagram** - Posts y stories (próximamente)

### 🔔 Tipos de Notificación
- **🔴 Stream** - Solo streams en vivo
- **📹 Video** - Solo videos nuevos
- **🔄 Both** - Ambos tipos (por defecto)

### ⚙️ Características Avanzadas
- **🔔 Pings automáticos** - Roles y usuarios específicos
- **🎨 Embeds personalizables** - Colores y mensajes únicos
- **⏰ Auto-eliminación** - Notificaciones temporales
- **📊 Base de datos** - Historial completo de notificaciones
- **🔄 Monitoreo automático** - Verificación cada 5-15 minutos
- **🎯 Filtros inteligentes** - Evita duplicados y spam

## 🚀 Instalación

### 1. Configurar Variables de Entorno

```bash
# Twitch API
TWITCH_CLIENT_ID=tu_client_id
TWITCH_CLIENT_SECRET=tu_client_secret

# YouTube API
YOUTUBE_API_KEY=tu_api_key

# Discord Webhook (opcional)
DISCORD_WEBHOOK_URL=tu_webhook_url
```

### 2. Obtener APIs

#### Twitch API
1. Ve a [https://dev.twitch.tv/console](https://dev.twitch.tv/console)
2. Crea una nueva aplicación
3. Copia el Client ID y Client Secret

#### YouTube API
1. Ve a [https://console.cloud.google.com/](https://console.cloud.google.com/)
2. Crea un proyecto o selecciona uno existente
3. Habilita la YouTube Data API v3
4. Crea credenciales (API Key)

### 3. Archivos Necesarios

Asegúrate de tener estos archivos en tu proyecto:
- `flozwer_notifications.py` - Sistema principal
- `flozwer_commands.py` - Comandos del sistema
- `flozwer_config.py` - Configuración

## 📋 Comandos Disponibles

### ⚙️ Configuración
```
!flozwerconfig setup                    - Configuración inicial del sistema
!flozwerconfig channel <nombre>         - Cambiar canal de notificaciones
!flozwerconfig role <nombre>            - Configurar rol para pings
!flozwerconfig color <#hex>             - Cambiar color de embeds
!flozwerconfig message <texto>          - Mensaje personalizado
!flozwerconfig autodelete <minutos>     - Auto-eliminar notificaciones
!flozwerconfig enable/disable           - Activar/desactivar sistema
```

### 📺 Gestión de Canales
```
!flozweradd <plataforma> <url> [tipo]   - Agregar canal para monitorear
!flozwerremove <plataforma> <nombre>    - Remover canal monitoreado
!flozwerlist                            - Listar todos los canales monitoreados
```

### 📊 Información y Pruebas
```
!flozwerstatus                          - Estado del sistema de notificaciones
!flozwertest                            - Enviar notificación de prueba
!flozwerhelp                            - Ayuda completa del sistema
```

## 🎮 Uso Rápido

### 1. Configuración Inicial
```
!flozwerconfig setup
```

### 2. Agregar un Canal de Twitch
```
!flozweradd twitch https://twitch.tv/tucanal
```

### 3. Agregar un Canal de YouTube
```
!flozweradd youtube https://youtube.com/@tucanal
```

### 4. Configurar Rol para Pings
```
!flozwerconfig role Notificaciones
```

### 5. Ver Estado del Sistema
```
!flozwerstatus
```

## 🔧 Configuración Avanzada

### Colores Personalizados
```
!flozwerconfig color #FF6B9D
```

### Mensaje Personalizado
```
!flozwerconfig message "¡{channel_name} está en vivo! 🎉"
```

### Auto-eliminación
```
!flozwerconfig autodelete 30  # Eliminar después de 30 minutos
```

### Tipos de Notificación
```
!flozweradd twitch https://twitch.tv/tucanal stream    # Solo streams
!flozweradd youtube https://youtube.com/@tucanal video # Solo videos
!flozweradd twitch https://twitch.tv/tucanal both      # Ambos
```

## 📊 Base de Datos

El sistema crea automáticamente una base de datos SQLite (`flozwer_notifications.db`) con las siguientes tablas:

- **server_config** - Configuración por servidor
- **monitored_channels** - Canales monitoreados
- **notification_history** - Historial de notificaciones
- **advanced_config** - Configuraciones avanzadas

## 🔄 Monitoreo Automático

- **Streams**: Verificación cada 5 minutos
- **Videos**: Verificación cada 15 minutos
- **Límites**: Máximo 10 notificaciones por hora
- **Filtros**: Evita duplicados automáticamente

## 🎨 Personalización de Embeds

### Colores por Plataforma
- **Twitch**: #9146FF (Púrpura)
- **YouTube**: #FF0000 (Rojo)
- **TikTok**: #000000 (Negro)
- **Instagram**: #E4405F (Rosa)

### Emojis por Plataforma
- **Twitch**: 📺
- **YouTube**: 🎥
- **TikTok**: 📱
- **Instagram**: 📸

### Emojis por Tipo
- **Stream**: 🔴
- **Video**: 📹

## 🛠️ Integración con el Bot

### 1. Agregar al Bot Principal
```python
# En tu archivo principal del bot
from flozwer_commands import FlozwerNotificationCommands

async def setup(bot):
    await bot.add_cog(FlozwerNotificationCommands(bot))
```

### 2. Verificar Configuración
```python
# Ejecutar para verificar APIs
python flozwer_config.py
```

## 🔍 Solución de Problemas

### Error: "API no configurada"
- Verifica que las variables de entorno estén configuradas
- Ejecuta `python flozwer_config.py` para ver el estado

### Error: "Canal no encontrado"
- Verifica que la URL del canal sea correcta
- Asegúrate de que el canal sea público

### Error: "No se pudo obtener información"
- Verifica que las APIs estén funcionando
- Revisa los logs del bot para más detalles

### Notificaciones no aparecen
- Verifica que el sistema esté habilitado: `!flozwerconfig`
- Asegúrate de que el canal de notificaciones esté configurado
- Revisa que el bot tenga permisos en el canal

## 📈 Estadísticas

El sistema mantiene estadísticas automáticas:
- Número de canales monitoreados por plataforma
- Historial de notificaciones enviadas
- Tiempo de respuesta de las APIs
- Estado de conectividad

## 🔮 Próximas Funcionalidades

- [ ] Soporte para TikTok
- [ ] Soporte para Instagram
- [ ] Notificaciones por webhook
- [ ] Filtros por palabras clave
- [ ] Horarios de notificación
- [ ] Estadísticas avanzadas
- [ ] Integración con más plataformas

## 📞 Soporte

Si tienes problemas o preguntas:
1. Revisa la configuración con `!flozwerhelp`
2. Verifica el estado con `!flozwerstatus`
3. Ejecuta una prueba con `!flozwertest`
4. Revisa los logs del bot

## 📄 Licencia

Este sistema es parte del bot de Discord y sigue las mismas condiciones de uso.

---

**¡Nunca más te pierdas contenido de tus creadores favoritos!** 🎉 