#!/usr/bin/env python3
"""
Sistema de Cifrado Seguro para Paimon Bot
Implementa cifrado AES-256-GCM con PBKDF2 para datos de usuario
"""

import os
import json
import hashlib
import secrets
from datetime import datetime
from typing import Dict, Optional, Tuple, Any
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.backends import default_backend

class PaimonCryptoManager:
    """Gestor de cifrado seguro para datos de usuario"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.salt_size = 16  # 16 bytes para salt
        self.iv_size = 12    # 12 bytes para IV (GCM)
        self.tag_size = 16   # 16 bytes para Auth Tag
        self.key_size = 32   # 32 bytes para AES-256
        self.iterations = 100000  # 100,000 iteraciones PBKDF2
        
        # Crear directorio data/ si no existe
        self._ensure_data_directory()
    
    def _ensure_data_directory(self):
        """Crear directorio data/ si no existe"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            print(f"📁 Directorio {self.data_dir}/ creado")
    
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derivar clave usando PBKDF2 con SHA-256"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_size,
            salt=salt,
            iterations=self.iterations,
            backend=default_backend()
        )
        return kdf.derive(password.encode('utf-8'))
    
    def _get_user_file_path(self, user_id: str) -> str:
        """Obtener ruta del archivo .Paimon para un usuario"""
        return os.path.join(self.data_dir, f"{user_id}.Paimon")
    
    def encrypt_user_data(self, user_id: str, username: str, password: str, 
                         additional_data: Dict[str, Any] = None) -> bool:
        """
        Cifrar y guardar datos de usuario
        
        Args:
            user_id: ID único del usuario
            username: Nombre de usuario
            password: Contraseña del usuario
            additional_data: Datos adicionales a cifrar
            
        Returns:
            bool: True si el cifrado fue exitoso
        """
        try:
            # Generar salt e IV aleatorios
            salt = secrets.token_bytes(self.salt_size)
            iv = secrets.token_bytes(self.iv_size)
            
            # Derivar clave maestra
            key = self._derive_key(password, salt)
            
            # Preparar datos a cifrar
            user_data = {
                'user_id': user_id,
                'username': username,
                'password_hash': hashlib.sha256(password.encode()).hexdigest(),
                'created_at': datetime.now().isoformat(),
                'last_login': None,
                'login_count': 0,
                'additional_data': additional_data or {}
            }
            
            # Convertir a JSON y cifrar
            plaintext = json.dumps(user_data, ensure_ascii=False).encode('utf-8')
            
            # Cifrar con AES-GCM
            aesgcm = AESGCM(key)
            ciphertext = aesgcm.encrypt(iv, plaintext, None)
            
            # El ciphertext de AESGCM ya incluye el auth tag al final
            # Estructura del archivo: SALT (16) + IV (12) + CIPHERTEXT_WITH_TAG
            file_data = salt + iv + ciphertext
            
            # Guardar en archivo .Paimon
            file_path = self._get_user_file_path(user_id)
            with open(file_path, 'wb') as f:
                f.write(file_data)
            
            # Limpiar clave de memoria
            key = b'\x00' * len(key)
            
            print(f"🔐 Usuario {username} registrado y cifrado exitosamente")
            return True
            
        except Exception as e:
            print(f"❌ Error cifrando datos de usuario: {e}")
            return False
    
    def decrypt_user_data(self, user_id: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Descifrar y validar datos de usuario
        
        Args:
            user_id: ID único del usuario
            password: Contraseña para descifrar
            
        Returns:
            Dict con datos del usuario o None si falla
        """
        try:
            file_path = self._get_user_file_path(user_id)
            
            # Verificar que el archivo existe
            if not os.path.exists(file_path):
                return None
            
            # Leer archivo .Paimon
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Verificar tamaño mínimo
            min_size = self.salt_size + self.iv_size + self.tag_size
            if len(file_data) < min_size:
                raise ValueError("Archivo corrupto: tamaño insuficiente")
            
            # Extraer componentes
            salt = file_data[:self.salt_size]
            iv = file_data[self.salt_size:self.salt_size + self.iv_size]
            ciphertext = file_data[self.salt_size + self.iv_size:]
            
            # Derivar clave con la misma sal
            key = self._derive_key(password, salt)
            
            # Descifrar con AES-GCM (incluye validación de integridad)
            aesgcm = AESGCM(key)
            plaintext = aesgcm.decrypt(iv, ciphertext, None)
            
            # Parsear JSON
            user_data = json.loads(plaintext.decode('utf-8'))
            
            # Validar contraseña
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if user_data.get('password_hash') != password_hash:
                raise ValueError("Contraseña incorrecta")
            
            # Actualizar último login
            user_data['last_login'] = datetime.now().isoformat()
            user_data['login_count'] = user_data.get('login_count', 0) + 1
            
            # Limpiar clave de memoria
            key = b'\x00' * len(key)
            
            return user_data
            
        except Exception as e:
            print(f"❌ Error descifrando datos de usuario: {e}")
            return None
    
    def change_password(self, user_id: str, old_password: str, new_password: str) -> bool:
        """
        Cambiar contraseña de usuario (rotación de clave)
        
        Args:
            user_id: ID único del usuario
            old_password: Contraseña actual
            new_password: Nueva contraseña
            
        Returns:
            bool: True si el cambio fue exitoso
        """
        try:
            # Descifrar con contraseña vieja
            user_data = self.decrypt_user_data(user_id, old_password)
            if not user_data:
                return False
            
            # Actualizar hash de contraseña
            user_data['password_hash'] = hashlib.sha256(new_password.encode()).hexdigest()
            user_data['password_changed_at'] = datetime.now().isoformat()
            
            # Volver a cifrar con nueva contraseña
            username = user_data.get('username', 'unknown')
            additional_data = user_data.get('additional_data', {})
            
            return self.encrypt_user_data(user_id, username, new_password, additional_data)
            
        except Exception as e:
            print(f"❌ Error cambiando contraseña: {e}")
            return False
    
    def user_exists(self, user_id: str) -> bool:
        """Verificar si un usuario ya está registrado"""
        file_path = self._get_user_file_path(user_id)
        return os.path.exists(file_path)
    
    def delete_user(self, user_id: str, password: str) -> bool:
        """
        Eliminar usuario (requiere contraseña para verificación)
        
        Args:
            user_id: ID único del usuario
            password: Contraseña para verificar identidad
            
        Returns:
            bool: True si la eliminación fue exitosa
        """
        try:
            # Verificar contraseña antes de eliminar
            user_data = self.decrypt_user_data(user_id, password)
            if not user_data:
                return False
            
            # Eliminar archivo
            file_path = self._get_user_file_path(user_id)
            os.remove(file_path)
            
            print(f"🗑️ Usuario {user_data.get('username')} eliminado exitosamente")
            return True
            
        except Exception as e:
            print(f"❌ Error eliminando usuario: {e}")
            return False
    
    def get_user_stats(self) -> Dict[str, Any]:
        """Obtener estadísticas de usuarios registrados"""
        try:
            files = [f for f in os.listdir(self.data_dir) if f.endswith('.Paimon')]

            return {
                'total_users': len(files),
                'data_directory': self.data_dir,
                'encryption_method': 'AES-256-GCM + PBKDF2-SHA256',
                'iterations': self.iterations,
                'files': files
            }

        except Exception as e:
            print(f"❌ Error obteniendo estadísticas: {e}")
            return {'total_users': 0, 'error': str(e)}

    def backup_to_github(self, github_token: str, repo_owner: str, repo_name: str) -> bool:
        """
        Hacer backup de todos los archivos .Paimon a GitHub

        Args:
            github_token: Token de acceso a GitHub
            repo_owner: Propietario del repositorio
            repo_name: Nombre del repositorio

        Returns:
            bool: True si el backup fue exitoso
        """
        try:
            import requests
            import base64

            files = [f for f in os.listdir(self.data_dir) if f.endswith('.Paimon')]

            if not files:
                print("📁 No hay archivos .Paimon para respaldar")
                return True

            headers = {
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            success_count = 0

            for file_name in files:
                try:
                    file_path = os.path.join(self.data_dir, file_name)

                    # Leer archivo cifrado
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    # Codificar en base64 para GitHub
                    encoded_content = base64.b64encode(file_content).decode('utf-8')

                    # Verificar si el archivo ya existe en GitHub
                    github_path = f"encrypted_users/{file_name}"
                    url = f'https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{github_path}'

                    response = requests.get(url, headers=headers)

                    payload = {
                        'message': f'Backup cifrado: {file_name}',
                        'content': encoded_content,
                        'branch': 'main'
                    }

                    # Si el archivo existe, necesitamos el SHA
                    if response.status_code == 200:
                        existing_data = response.json()
                        payload['sha'] = existing_data['sha']

                    # Subir/actualizar archivo
                    response = requests.put(url, headers=headers, json=payload)

                    if response.status_code in [200, 201]:
                        success_count += 1
                        print(f"✅ {file_name} respaldado en GitHub")
                    else:
                        print(f"❌ Error respaldando {file_name}: {response.status_code}")

                except Exception as e:
                    print(f"❌ Error procesando {file_name}: {e}")

            print(f"📦 Backup completado: {success_count}/{len(files)} archivos")
            return success_count == len(files)

        except Exception as e:
            print(f"❌ Error en backup a GitHub: {e}")
            return False

    def restore_from_github(self, github_token: str, repo_owner: str, repo_name: str) -> bool:
        """
        Restaurar archivos .Paimon desde GitHub

        Args:
            github_token: Token de acceso a GitHub
            repo_owner: Propietario del repositorio
            repo_name: Nombre del repositorio

        Returns:
            bool: True si la restauración fue exitosa
        """
        try:
            import requests
            import base64

            headers = {
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            # Listar archivos en el directorio encrypted_users
            url = f'https://api.github.com/repos/{repo_owner}/{repo_name}/contents/encrypted_users'
            response = requests.get(url, headers=headers)

            if response.status_code != 200:
                print("📁 No se encontró directorio encrypted_users en GitHub")
                return False

            files_data = response.json()

            if not isinstance(files_data, list):
                print("❌ Respuesta inesperada de GitHub")
                return False

            success_count = 0

            for file_info in files_data:
                if file_info['name'].endswith('.Paimon'):
                    try:
                        # Descargar archivo
                        file_response = requests.get(file_info['download_url'], headers=headers)

                        if file_response.status_code == 200:
                            # Decodificar contenido
                            encoded_content = file_response.json()['content']
                            file_content = base64.b64decode(encoded_content)

                            # Guardar archivo local
                            local_path = os.path.join(self.data_dir, file_info['name'])
                            with open(local_path, 'wb') as f:
                                f.write(file_content)

                            success_count += 1
                            print(f"✅ {file_info['name']} restaurado desde GitHub")
                        else:
                            print(f"❌ Error descargando {file_info['name']}")

                    except Exception as e:
                        print(f"❌ Error procesando {file_info['name']}: {e}")

            print(f"📥 Restauración completada: {success_count} archivos")
            return success_count > 0

        except Exception as e:
            print(f"❌ Error en restauración desde GitHub: {e}")
            return False

# Instancia global del gestor de cifrado
crypto_manager = PaimonCryptoManager()
