import discord
from discord.ext import commands
import json
import re
from typing import Dict, List, Optional
from datetime import datetime
from flozwer_notifications import FlozwerNotifications

class FlozwerNotificationCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.notifications = FlozwerNotifications(bot)
    
    @commands.command(name="flozwerconfig", help="Configura el sistema de notificaciones FlozWer")
    @commands.has_permissions(administrator=True)
    async def flozwer_config(self, ctx, option: str = None, *, value: str = None):
        """Configura el sistema de notificaciones"""
        if not option:
            # Mostrar configuración actual
            config = self.notifications.get_server_config(ctx.guild.id)
            if not config:
                embed = discord.Embed(
                    title="⚙️ Configuración FlozWer Notifications",
                    description="El sistema no está configurado. Usa `!flozwerconfig setup` para configurarlo.",
                    color=0xFF6B9D
                )
                await ctx.send(embed=embed)
                return
            
            embed = discord.Embed(
                title="⚙️ Configuración FlozWer Notifications",
                color=0xFF6B9D
            )
            
            channel = ctx.guild.get_channel(config['notification_channel_id'])
            embed.add_field(name="📺 Canal", value=channel.mention if channel else "No configurado", inline=True)
            embed.add_field(name="🔔 Estado", value="✅ Activado" if config['enabled'] else "❌ Desactivado", inline=True)
            
            if config.get('ping_role_id'):
                role = ctx.guild.get_role(config['ping_role_id'])
                embed.add_field(name="🎯 Rol", value=role.mention if role else "Rol no encontrado", inline=True)
            
            embed.add_field(name="🎨 Color", value=config.get('embed_color', '#FF6B9D'), inline=True)
            embed.add_field(name="⏰ Auto-delete", value=f"{config.get('auto_delete_minutes', 0)} minutos" if config.get('auto_delete_minutes', 0) > 0 else "Desactivado", inline=True)
            
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "setup":
            # Configuración inicial
            self.notifications.set_server_config(
                ctx.guild.id,
                notification_channel_id=ctx.channel.id,
                enabled=True
            )
            
            embed = discord.Embed(
                title="✅ Configuración Inicial Completada",
                description=f"El sistema de notificaciones FlozWer ha sido configurado en {ctx.channel.mention}",
                color=0x00FF00
            )
            embed.add_field(name="📋 Próximos pasos", value="1. Usa `!flozweradd` para agregar canales\n2. Usa `!flozwerconfig` para más opciones", inline=False)
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "channel" and value:
            # Configurar canal de notificaciones
            channel = discord.utils.get(ctx.guild.channels, name=value)
            if not channel:
                await ctx.send("❌ Canal no encontrado. Usa el nombre del canal.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                notification_channel_id=channel.id
            )
            
            embed = discord.Embed(
                title="✅ Canal Configurado",
                description=f"Las notificaciones se enviarán a {channel.mention}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "role" and value:
            # Configurar rol para pings
            role = discord.utils.get(ctx.guild.roles, name=value)
            if not role:
                await ctx.send("❌ Rol no encontrado. Usa el nombre del rol.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                ping_role_id=role.id
            )
            
            embed = discord.Embed(
                title="✅ Rol Configurado",
                description=f"Se hará ping a {role.mention} en las notificaciones",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "color" and value:
            # Configurar color del embed
            if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
                await ctx.send("❌ Color inválido. Usa formato hexadecimal (ej: #FF6B9D)")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                embed_color=value
            )
            
            embed = discord.Embed(
                title="✅ Color Configurado",
                description=f"Color de embeds cambiado a {value}",
                color=int(value.replace('#', ''), 16)
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "message" and value:
            # Configurar mensaje personalizado
            self.notifications.set_server_config(
                ctx.guild.id,
                custom_message=value
            )
            
            embed = discord.Embed(
                title="✅ Mensaje Personalizado",
                description=f"Mensaje configurado: {value}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "autodelete" and value:
            # Configurar auto-delete
            try:
                minutes = int(value)
                if minutes < 0 or minutes > 1440:  # Máximo 24 horas
                    await ctx.send("❌ Los minutos deben estar entre 0 y 1440")
                    return
            except ValueError:
                await ctx.send("❌ Valor inválido. Usa un número de minutos.")
                return
            
            self.notifications.set_server_config(
                ctx.guild.id,
                auto_delete_minutes=minutes
            )
            
            embed = discord.Embed(
                title="✅ Auto-delete Configurado",
                description=f"Las notificaciones se eliminarán automáticamente después de {minutes} minutos" if minutes > 0 else "Auto-delete desactivado",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "enable":
            # Habilitar sistema
            self.notifications.set_server_config(
                ctx.guild.id,
                enabled=True
            )
            
            embed = discord.Embed(
                title="✅ Sistema Habilitado",
                description="El sistema de notificaciones FlozWer está ahora activo",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
            return
        
        if option.lower() == "disable":
            # Deshabilitar sistema
            self.notifications.set_server_config(
                ctx.guild.id,
                enabled=False
            )
            
            embed = discord.Embed(
                title="❌ Sistema Deshabilitado",
                description="El sistema de notificaciones FlozWer está ahora inactivo",
                color=0xFF0000
            )
            await ctx.send(embed=embed)
            return
        
        await ctx.send("❌ Opción no válida. Usa `!flozwerconfig` para ver las opciones disponibles.")
    
    @commands.command(name="flozweradd", help="Agrega un canal para monitorear")
    @commands.has_permissions(administrator=True)
    async def flozwer_add(self, ctx, platform: str, channel_url: str, notification_type: str = "both"):
        """Agrega un canal para monitorear"""
        platform = platform.lower()
        if platform not in ['twitch', 'youtube', 'tiktok', 'instagram']:
            await ctx.send("❌ Plataforma no soportada. Plataformas válidas: twitch, youtube, tiktok, instagram")
            return
        
        if notification_type not in ['stream', 'video', 'both']:
            await ctx.send("❌ Tipo de notificación inválido. Tipos válidos: stream, video, both")
            return
        
        # Extraer información del canal según la plataforma
        channel_info = await self.extract_channel_info(platform, channel_url)
        if not channel_info:
            await ctx.send("❌ No se pudo obtener información del canal. Verifica la URL.")
            return
        
        # Agregar canal
        success = self.notifications.add_monitored_channel(
            ctx.guild.id, platform, channel_info['id'], 
            channel_info['name'], channel_url, notification_type
        )
        
        if success:
            embed = discord.Embed(
                title="✅ Canal Agregado",
                description=f"**Canal:** {channel_info['name']}\n**Plataforma:** {platform.title()}\n**Tipo:** {notification_type}",
                color=0x00FF00
            )
            embed.add_field(name="🔗 URL", value=channel_url, inline=False)
            await ctx.send(embed=embed)
        else:
            await ctx.send("❌ Error al agregar el canal.")
    
    async def extract_channel_info(self, platform: str, url: str) -> Optional[Dict]:
        """Extrae información del canal desde la URL"""
        try:
            if platform == 'twitch':
                # Extraer nombre de usuario de Twitch
                match = re.search(r'twitch\.tv/([^/?]+)', url)
                if match:
                    username = match.group(1)
                    return {
                        'id': username,
                        'name': username
                    }
            
            elif platform == 'youtube':
                # Extraer ID del canal de YouTube
                match = re.search(r'youtube\.com/(?:channel/|c/|user/)?([^/?]+)', url)
                if match:
                    channel_id = match.group(1)
                    return {
                        'id': channel_id,
                        'name': f"YouTube Channel ({channel_id})"
                    }
            
            # Para otras plataformas, usar el nombre como ID por ahora
            return {
                'id': url,
                'name': f"{platform.title()} Channel"
            }
            
        except Exception as e:
            print(f"Error extracting channel info: {e}")
            return None
    
    @commands.command(name="flozwerremove", help="Remueve un canal monitoreado")
    @commands.has_permissions(administrator=True)
    async def flozwer_remove(self, ctx, platform: str, channel_name: str):
        """Remueve un canal monitoreado"""
        platform = platform.lower()
        if platform not in ['twitch', 'youtube', 'tiktok', 'instagram']:
            await ctx.send("❌ Plataforma no soportada.")
            return
        
        # Buscar el canal
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        target_channel = None
        
        for channel in channels:
            if channel['platform'] == platform and channel['channel_name'].lower() == channel_name.lower():
                target_channel = channel
                break
        
        if not target_channel:
            await ctx.send("❌ Canal no encontrado.")
            return
        
        # Remover canal
        success = self.notifications.remove_monitored_channel(
            ctx.guild.id, platform, target_channel['channel_id']
        )
        
        if success:
            embed = discord.Embed(
                title="✅ Canal Removido",
                description=f"**Canal:** {target_channel['channel_name']}\n**Plataforma:** {platform.title()}",
                color=0x00FF00
            )
            await ctx.send(embed=embed)
        else:
            await ctx.send("❌ Error al remover el canal.")
    
    @commands.command(name="flozwerlist", help="Lista todos los canales monitoreados")
    async def flozwer_list(self, ctx):
        """Lista todos los canales monitoreados"""
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        
        if not channels:
            embed = discord.Embed(
                title="📋 Canales Monitoreados",
                description="No hay canales configurados.",
                color=0xFF6B9D
            )
            await ctx.send(embed=embed)
            return
        
        embed = discord.Embed(
            title="📋 Canales Monitoreados",
            color=0xFF6B9D
        )
        
        for channel in channels:
            status = "✅" if channel['enabled'] else "❌"
            embed.add_field(
                name=f"{status} {channel['channel_name']}",
                value=f"**Plataforma:** {channel['platform'].title()}\n**Tipo:** {channel['notification_type']}\n**URL:** {channel['channel_url']}",
                inline=False
            )
        
        await ctx.send(embed=embed)
    
    @commands.command(name="flozwerstatus", help="Muestra el estado del sistema de notificaciones")
    async def flozwer_status(self, ctx):
        """Muestra el estado del sistema"""
        config = self.notifications.get_server_config(ctx.guild.id)
        channels = self.notifications.get_monitored_channels(ctx.guild.id)
        
        embed = discord.Embed(
            title="📊 Estado FlozWer Notifications",
            color=0xFF6B9D
        )
        
        if config:
            embed.add_field(name="🔔 Sistema", value="✅ Activo" if config['enabled'] else "❌ Inactivo", inline=True)
            channel = ctx.guild.get_channel(config['notification_channel_id'])
            embed.add_field(name="📺 Canal", value=channel.mention if channel else "No configurado", inline=True)
            embed.add_field(name="📋 Canales", value=str(len(channels)), inline=True)
        else:
            embed.add_field(name="🔔 Sistema", value="❌ No configurado", inline=True)
        
        # Estadísticas de plataformas
        platforms = {}
        for channel in channels:
            platform = channel['platform']
            platforms[platform] = platforms.get(platform, 0) + 1
        
        if platforms:
            platform_stats = "\n".join([f"• {p.title()}: {c}" for p, c in platforms.items()])
            embed.add_field(name="📈 Plataformas", value=platform_stats, inline=False)
        
        await ctx.send(embed=embed)
    
    @commands.command(name="flozwertest", help="Envía una notificación de prueba")
    @commands.has_permissions(administrator=True)
    async def flozwer_test(self, ctx):
        """Envía una notificación de prueba"""
        config = self.notifications.get_server_config(ctx.guild.id)
        if not config:
            await ctx.send("❌ El sistema no está configurado.")
            return
        
        # Crear datos de prueba
        test_data = {
            'channel_name': 'Canal de Prueba',
            'title': 'Stream de Prueba',
            'url': 'https://example.com',
            'game_name': 'Juego de Prueba',
            'viewer_count': 1000
        }
        
        await self.notifications.send_notification(ctx.guild.id, 'twitch', 'stream', test_data)
        await ctx.send("✅ Notificación de prueba enviada.")
    
    @commands.command(name="flozwerhelp", help="Muestra ayuda del sistema FlozWer")
    async def flozwer_help(self, ctx):
        """Muestra ayuda del sistema"""
        embed = discord.Embed(
            title="📚 Ayuda FlozWer Notifications",
            description="Sistema de notificaciones para múltiples plataformas",
            color=0xFF6B9D
        )
        
        embed.add_field(
            name="⚙️ Configuración",
            value="""
            `!flozwerconfig setup` - Configuración inicial
            `!flozwerconfig channel <nombre>` - Cambiar canal
            `!flozwerconfig role <nombre>` - Configurar rol para pings
            `!flozwerconfig color <#hex>` - Cambiar color de embeds
            `!flozwerconfig message <texto>` - Mensaje personalizado
            `!flozwerconfig autodelete <minutos>` - Auto-eliminar notificaciones
            `!flozwerconfig enable/disable` - Activar/desactivar sistema
            """,
            inline=False
        )
        
        embed.add_field(
            name="📺 Gestión de Canales",
            value="""
            `!flozweradd <plataforma> <url> [tipo]` - Agregar canal
            `!flozwerremove <plataforma> <nombre>` - Remover canal
            `!flozwerlist` - Listar canales
            """,
            inline=False
        )
        
        embed.add_field(
            name="📊 Información",
            value="""
            `!flozwerstatus` - Estado del sistema
            `!flozwertest` - Enviar notificación de prueba
            `!flozwerhelp` - Esta ayuda
            """,
            inline=False
        )
        
        embed.add_field(
            name="🎯 Plataformas Soportadas",
            value="• Twitch (streams)\n• YouTube (videos y streams)\n• TikTok (videos)\n• Instagram (posts)",
            inline=False
        )
        
        embed.add_field(
            name="📋 Tipos de Notificación",
            value="• `stream` - Solo streams en vivo\n• `video` - Solo videos\n• `both` - Ambos (por defecto)",
            inline=False
        )
        
        embed.set_footer(text="FlozWer Notifications System")
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(FlozwerNotificationCommands(bot)) 