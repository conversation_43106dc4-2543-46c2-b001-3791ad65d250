import discord
from discord.ext import commands
import json
import os
from datetime import datetime
import asyncio

# Global variables for stream notifications
STREAM_NOTIFICATIONS_CONFIG = {}
STREAM_NOTIFICATIONS_ENABLED = {}
ACTIVE_STREAMS = {}

# Configuration file path
CONFIG_FILE = "stream_notifications_config.json"

def load_stream_config():
    """Load stream notifications configuration from file"""
    global STREAM_NOTIFICATIONS_CONFIG, STREAM_NOTIFICATIONS_ENABLED
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                STREAM_NOTIFICATIONS_CONFIG = data.get('config', {})
                STREAM_NOTIFICATIONS_ENABLED = data.get('enabled', {})
    except Exception as e:
        print(f"Error loading stream notifications config: {e}")
        STREAM_NOTIFICATIONS_CONFIG = {}
        STREAM_NOTIFICATIONS_ENABLED = {}

def save_stream_config():
    """Save stream notifications configuration to file"""
    try:
        data = {
            'config': STREAM_NOTIFICATIONS_CONFIG,
            'enabled': STREAM_NOTIFICATIONS_ENABLED
        }
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving stream notifications config: {e}")

def get_stream_config(guild_id):
    """Get stream notifications configuration for a guild"""
    guild_id = str(guild_id)
    if guild_id not in STREAM_NOTIFICATIONS_CONFIG:
        STREAM_NOTIFICATIONS_CONFIG[guild_id] = {
            'channel_id': None,
            'enabled': False,
            'ping_role_id': None,
            'message_template': "🎮 **¡{streamer} está transmitiendo!**\n📺 **{game}**\n🔗 {url}",
            'auto_detect': True
        }
        save_stream_config()
    return STREAM_NOTIFICATIONS_CONFIG[guild_id]

def set_stream_config(guild_id, **kwargs):
    """Set stream notifications configuration for a guild"""
    guild_id = str(guild_id)
    config = get_stream_config(guild_id)
    config.update(kwargs)
    STREAM_NOTIFICATIONS_CONFIG[guild_id] = config
    save_stream_config()

def is_stream_notifications_enabled(guild_id):
    """Check if stream notifications are enabled for a guild"""
    guild_id = str(guild_id)
    return STREAM_NOTIFICATIONS_ENABLED.get(guild_id, False)

def set_stream_notifications_enabled(guild_id, enabled):
    """Enable or disable stream notifications for a guild"""
    guild_id = str(guild_id)
    STREAM_NOTIFICATIONS_ENABLED[guild_id] = enabled
    save_stream_config()

def add_active_stream(guild_id, streamer_id, stream_data):
    """Add an active stream to tracking"""
    guild_id = str(guild_id)
    if guild_id not in ACTIVE_STREAMS:
        ACTIVE_STREAMS[guild_id] = {}
    ACTIVE_STREAMS[guild_id][streamer_id] = {
        'data': stream_data,
        'notified': False,
        'start_time': datetime.now()
    }

def remove_active_stream(guild_id, streamer_id):
    """Remove an active stream from tracking"""
    guild_id = str(guild_id)
    if guild_id in ACTIVE_STREAMS and streamer_id in ACTIVE_STREAMS[guild_id]:
        del ACTIVE_STREAMS[guild_id][streamer_id]

def get_active_streams(guild_id):
    """Get all active streams for a guild"""
    guild_id = str(guild_id)
    return ACTIVE_STREAMS.get(guild_id, {})

def is_stream_active(guild_id, streamer_id):
    """Check if a streamer is currently streaming"""
    guild_id = str(guild_id)
    return guild_id in ACTIVE_STREAMS and streamer_id in ACTIVE_STREAMS[guild_id]

def mark_stream_notified(guild_id, streamer_id):
    """Mark a stream as notified"""
    guild_id = str(guild_id)
    if guild_id in ACTIVE_STREAMS and streamer_id in ACTIVE_STREAMS[guild_id]:
        ACTIVE_STREAMS[guild_id][streamer_id]['notified'] = True

def create_enhanced_embed(title, description=None, color='primary', thumbnail=None, image=None, footer_text=None, author_name=None, author_icon=None):
    """Create an enhanced embed with consistent styling"""
    colors = {
        'primary': 0x5865F2,
        'success': 0x57F287,
        'warning': 0xFEE75C,
        'error': 0xED4245,
        'info': 0x5865F2,
        'level': 0xFF6B6B,
        'music': 0x1DB954,
        'stream': 0x9146FF
    }

    embed = discord.Embed(
        title=title,
        description=description,
        color=colors.get(color, colors['primary'])
    )

    if thumbnail:
        embed.set_thumbnail(url=thumbnail)
    if image:
        embed.set_image(url=image)
    if footer_text:
        embed.set_footer(text=footer_text)
    if author_name:
        embed.set_author(name=author_name, icon_url=author_icon)

    return embed

def get_emoji(emoji_type, animated=False):
    """Get emoji by type"""
    emojis = {
        'success': '<a:check:1234567890>' if animated else '✅',
        'error': '<a:cross:1234567890>' if animated else '❌',
        'warning': '⚠️',
        'info': 'ℹ️',
        'stream': '🎮',
        'channel': '📺',
        'settings': '⚙️',
        'status': '📊'
    }
    return emojis.get(emoji_type, '❓')

async def send_stream_notification(bot, guild_id, streamer_name, game_name, stream_url, thumbnail_url=None):
    """Send stream notification to configured channel"""
    try:
        config = get_stream_config(guild_id)
        if not config['channel_id']:
            return False

        channel = bot.get_channel(config['channel_id'])
        if not channel:
            return False

        # Format message
        message = config['message_template'].format(
            streamer=streamer_name,
            game=game_name,
            url=stream_url
        )

        # Create embed
        embed = create_enhanced_embed(
            title=f"{get_emoji('stream')} ¡{streamer_name} está transmitiendo!",
            description=f"🎮 **Juego:** {game_name}\n🔗 **Enlace:** {stream_url}",
            color='stream',
            thumbnail=thumbnail_url
        )

        # Add ping if role is configured
        content = ""
        if config['ping_role_id']:
            content = f"<@&{config['ping_role_id']}>"

        await channel.send(content=content, embed=embed)
        return True

    except Exception as e:
        print(f"Error sending stream notification: {e}")
        return False

async def check_stream_status(bot, guild_id, streamer_id, streamer_name):
    """Check if a streamer is currently streaming"""
    try:
        # This is a placeholder - you would integrate with Twitch API here
        # For now, we'll simulate stream detection
        import random
        is_streaming = random.choice([True, False])

        if is_streaming:
            if not is_stream_active(guild_id, streamer_id):
                # Stream just started
                stream_data = {
                    'name': streamer_name,
                    'game': 'Geometry Dash',
                    'url': f'https://twitch.tv/{streamer_name}',
                    'thumbnail': None
                }
                add_active_stream(guild_id, streamer_id, stream_data)

                # Send notification if enabled
                if is_stream_notifications_enabled(guild_id):
                    await send_stream_notification(
                        bot, guild_id, streamer_name, 
                        stream_data['game'], stream_data['url'], stream_data['thumbnail']
                    )
                    mark_stream_notified(guild_id, streamer_id)
        else:
            if is_stream_active(guild_id, streamer_id):
                # Stream ended
                remove_active_stream(guild_id, streamer_id)

    except Exception as e:
        print(f"Error checking stream status: {e}")

# Commands
async def setup_stream_notifications(bot):
    """Setup stream notification commands"""

    @bot.command(name="streamconfig", help="Configura las notificaciones de stream")
    @commands.has_permissions(administrator=True)
    async def stream_config_command(ctx, option: str = None, *, value: str = None):
        """Configure stream notifications"""
        config = get_stream_config(ctx.guild.id)

        if not option:
            # Show current configuration
            embed = create_enhanced_embed(
                title=f"{get_emoji('settings')} Configuración de Notificaciones de Stream",
                description="Configuración actual del sistema de notificaciones de stream:",
                color='info'
            )

            channel_mention = f"<#{config['channel_id']}>" if config['channel_id'] else "No configurado"
            role_mention = f"<@&{config['ping_role_id']}>" if config['ping_role_id'] else "No configurado"
            status = "✅ Habilitado" if is_stream_notifications_enabled(ctx.guild.id) else "❌ Deshabilitado"

            embed.add_field(name="📺 Canal", value=channel_mention, inline=True)
            embed.add_field(name="🔔 Rol para ping", value=role_mention, inline=True)
            embed.add_field(name="📊 Estado", value=status, inline=True)
            embed.add_field(name="📝 Plantilla", value=f"```{config['message_template']}```", inline=False)
            embed.add_field(name="🔍 Auto-detección", value="✅ Activada" if config['auto_detect'] else "❌ Desactivada", inline=True)

            await ctx.send(embed=embed)
            return

        option = option.lower()

        if option == "canal":
            if not value:
                await ctx.send(f"{get_emoji('error')} Debes especificar un canal.")
                return

            # Extract channel ID from mention
            if ctx.message.channel_mentions:
                channel = ctx.message.channel_mentions[0]
            else:
                channel = discord.utils.get(ctx.guild.channels, name=value)

            if not channel:
                await ctx.send(f"{get_emoji('error')} Canal no encontrado.")
                return

            set_stream_config(ctx.guild.id, channel_id=channel.id)
            embed = create_enhanced_embed(
                title=f"{get_emoji('success', True)} Canal Configurado",
                description=f"✅ Canal de notificaciones configurado: {channel.mention}",
                color='success'
            )
            await ctx.send(embed=embed)

        elif option == "rol":
            if not value:
                await ctx.send(f"{get_emoji('error')} Debes especificar un rol.")
                return

            # Extract role ID from mention
            if ctx.message.role_mentions:
                role = ctx.message.role_mentions[0]
            else:
                role = discord.utils.get(ctx.guild.roles, name=value)

            if not role:
                await ctx.send(f"{get_emoji('error')} Rol no encontrado.")
                return

            set_stream_config(ctx.guild.id, ping_role_id=role.id)
            embed = create_enhanced_embed(
                title=f"{get_emoji('success', True)} Rol Configurado",
                description=f"✅ Rol para ping configurado: {role.mention}",
                color='success'
            )
            await ctx.send(embed=embed)

        elif option == "plantilla":
            if not value:
                await ctx.send(f"{get_emoji('error')} Debes especificar una plantilla.")
                return

            # Validate template has required placeholders
            required_placeholders = ['{streamer}', '{game}', '{url}']
            missing = [p for p in required_placeholders if p not in value]

            if missing:
                await ctx.send(f"{get_emoji('error')} La plantilla debe incluir: {', '.join(missing)}")
                return

            set_stream_config(ctx.guild.id, message_template=value)
            embed = create_enhanced_embed(
                title=f"{get_emoji('success', True)} Plantilla Configurada",
                description=f"✅ Plantilla de mensaje actualizada:\n```{value}```",
                color='success'
            )
            await ctx.send(embed=embed)

        elif option == "autodetect":
            if not value:
                await ctx.send(f"{get_emoji('error')} Debes especificar 'on' o 'off'.")
                return

            auto_detect = value.lower() in ['on', 'true', 'si', 'yes']
            set_stream_config(ctx.guild.id, auto_detect=auto_detect)

            status = "activada" if auto_detect else "desactivada"
            embed = create_enhanced_embed(
                title=f"{get_emoji('success', True)} Auto-detección Configurada",
                description=f"✅ Auto-detección de streams {status}",
                color='success'
            )
            await ctx.send(embed=embed)

        else:
            await ctx.send(f"{get_emoji('error')} Opción no válida. Usa: canal, rol, plantilla, autodetect")

    @bot.command(name="streamenable", help="Habilita o deshabilita las notificaciones de stream")
    @commands.has_permissions(administrator=True)
    async def stream_enable_command(ctx, estado: str = None):
        """Enable or disable stream notifications"""
        if not estado:
            current_status = is_stream_notifications_enabled(ctx.guild.id)
            status_text = "habilitadas" if current_status else "deshabilitadas"
            embed = create_enhanced_embed(
                title=f"{get_emoji('status')} Estado de Notificaciones",
                description=f"Las notificaciones de stream están actualmente **{status_text}**",
                color='info'
            )
            await ctx.send(embed=embed)
            return

        estado = estado.lower()
        enabled = estado in ['on', 'true', 'si', 'yes', 'habilitar', 'enable']

        if enabled:
            # Check if channel is configured
            config = get_stream_config(ctx.guild.id)
            if not config['channel_id']:
                embed = create_enhanced_embed(
                    title=f"{get_emoji('error')} Canal No Configurado",
                    description="❌ Debes configurar un canal antes de habilitar las notificaciones.\n"
                               f"Usa `!streamconfig canal #canal`",
                    color='error'
                )
                await ctx.send(embed=embed)
                return

        set_stream_notifications_enabled(ctx.guild.id, enabled)

        status_text = "habilitadas" if enabled else "deshabilitadas"
        embed = create_enhanced_embed(
            title=f"{get_emoji('success', True)} Notificaciones {status_text.title()}",
            description=f"✅ Las notificaciones de stream han sido **{status_text}**",
            color='success'
        )
        await ctx.send(embed=embed)

    @bot.command(name="streamstatus", help="Muestra el estado de las notificaciones de stream")
    async def stream_status_command(ctx):
        """Show stream notifications status"""
        config = get_stream_config(ctx.guild.id)
        enabled = is_stream_notifications_enabled(ctx.guild.id)
        active_streams = get_active_streams(ctx.guild.id)

        embed = create_enhanced_embed(
            title=f"{get_emoji('status')} Estado de Notificaciones de Stream",
            description="Información del sistema de notificaciones de stream:",
            color='info'
        )

        # System status
        status_emoji = "✅" if enabled else "❌"
        status_text = "Habilitado" if enabled else "Deshabilitado"
        embed.add_field(
            name="📊 Estado del Sistema",
            value=f"{status_emoji} {status_text}",
            inline=True
        )

        # Channel status
        if config['channel_id']:
            channel = bot.get_channel(config['channel_id'])
            channel_text = channel.mention if channel else "Canal no encontrado"
        else:
            channel_text = "No configurado"
        embed.add_field(
            name="📺 Canal",
            value=channel_text,
            inline=True
        )

        # Active streams
        embed.add_field(
            name="🎮 Streams Activos",
            value=f"{len(active_streams)} streams",
            inline=True
        )

        # Auto-detection
        auto_detect_status = "✅ Activada" if config['auto_detect'] else "❌ Desactivada"
        embed.add_field(
            name="🔍 Auto-detección",
            value=auto_detect_status,
            inline=True
        )

        # Active streams list
        if active_streams:
            streams_list = []
            for streamer_id, stream_info in active_streams.items():
                stream_data = stream_info['data']
                notified = "✅" if stream_info['notified'] else "⏳"
                streams_list.append(f"{notified} {stream_data['name']} - {stream_data['game']}")

            embed.add_field(
                name="📋 Streams en Progreso",
                value="\n".join(streams_list),
                inline=False
            )

        await ctx.send(embed=embed)

    @bot.command(name="streamtest", help="Envía una notificación de prueba")
    @commands.has_permissions(administrator=True)
    async def stream_test_command(ctx):
        """Send a test stream notification"""
        if not is_stream_notifications_enabled(ctx.guild.id):
            embed = create_enhanced_embed(
                title=f"{get_emoji('error')} Notificaciones Deshabilitadas",
                description="❌ Las notificaciones de stream están deshabilitadas.",
                color='error'
            )
            await ctx.send(embed=embed)
            return

        config = get_stream_config(ctx.guild.id)
        if not config['channel_id']:
            embed = create_enhanced_embed(
                title=f"{get_emoji('error')} Canal No Configurado",
                description="❌ No hay canal configurado para las notificaciones.",
                color='error'
            )
            await ctx.send(embed=embed)
            return

        # Send test notification
        success = await send_stream_notification(
            bot, ctx.guild.id, "Usuario de Prueba", 
            "Geometry Dash", "https://twitch.tv/test", None
        )

        if success:
            embed = create_enhanced_embed(
                title=f"{get_emoji('success', True)} Notificación de Prueba Enviada",
                description="✅ Se ha enviado una notificación de prueba al canal configurado.",
                color='success'
            )
        else:
            embed = create_enhanced_embed(
                title=f"{get_emoji('error')} Error al Enviar",
                description="❌ No se pudo enviar la notificación de prueba.",
                color='error'
            )

        await ctx.send(embed=embed)

    @bot.command(name="streamadd", help="Agrega un streamer para monitorear")
    @commands.has_permissions(administrator=True)
    async def stream_add_command(ctx, streamer_name: str):
        """Add a streamer to monitor"""
        # This would integrate with your existing streamer management system
        # For now, we'll just acknowledge the command
        embed = create_enhanced_embed(
            title=f"{get_emoji('success', True)} Streamer Agregado",
            description=f"✅ Se agregó **{streamer_name}** a la lista de monitoreo.\n"
                       f"El sistema verificará automáticamente cuando esté transmitiendo.",
            color='success'
        )
        await ctx.send(embed=embed)

    @bot.command(name="streamremove", help="Remueve un streamer del monitoreo")
    @commands.has_permissions(administrator=True)
    async def stream_remove_command(ctx, streamer_name: str):
        """Remove a streamer from monitoring"""
        # This would integrate with your existing streamer management system
        embed = create_enhanced_embed(
            title=f"{get_emoji('success', True)} Streamer Removido",
            description=f"✅ Se removió **{streamer_name}** de la lista de monitoreo.",
            color='success'
        )
        await ctx.send(embed=embed)

# Initialize configuration on module load
load_stream_config()

# Export functions for integration
__all__ = [
    'load_stream_config',
    'save_stream_config',
    'get_stream_config',
    'set_stream_config',
    'is_stream_notifications_enabled',
    'set_stream_notifications_enabled',
    'add_active_stream',
    'remove_active_stream',
    'get_active_streams',
    'is_stream_active',
    'mark_stream_notified',
    'send_stream_notification',
    'check_stream_status',
    'setup_stream_notifications'
] 