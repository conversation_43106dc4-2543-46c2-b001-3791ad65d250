# ==========================================
# PAIMON BOT - REQUIREMENTS COMPLETO
# Versión 2.1.95 - Actualizado 2024
# ==========================================

# ==========================================
# CORE - Discord Bot Framework
# ==========================================
discord.py>=2.3.2
aiohttp>=3.9.0
python-dotenv>=1.0.0

# ==========================================
# CRIPTOGRAFÍA Y SEGURIDAD (Cuentas Encriptadas)
# ==========================================
cryptography>=41.0.0
bcrypt>=4.0.0
passlib[bcrypt,argon2]>=1.7.4
argon2-cffi>=23.0.0
pycryptodome>=3.19.0

# ==========================================
# AUTENTICACIÓN Y TOKENS
# ==========================================
PyJWT>=2.8.0
python-jose[cryptography]>=3.3.0
itsdangerous>=2.1.0

# ==========================================
# BASE DE DATOS Y PERSISTENCIA
# ==========================================
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# ==========================================
# VALIDACIÓN Y UTILIDADES DE SEGURIDAD
# ==========================================
email-validator>=2.1.0
validators>=0.22.0
secure>=0.3.0

# ==========================================
# NETWORKING Y APIs
# ==========================================
requests>=2.31.0
urllib3>=2.0.0
httpx>=0.25.0
beautifulsoup4>=4.12.0

# ==========================================
# GEOMETRY DASH
# ==========================================
gd.py>=1.0.1
wrapt>=1.15.0

# ==========================================
# INTELIGENCIA ARTIFICIAL
# ==========================================
google-generativeai>=0.3.0
openai>=1.0.0

# ==========================================
# PROCESAMIENTO DE IMÁGENES
# ==========================================
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# ==========================================
# AUDIO Y MULTIMEDIA
# ==========================================
yt-dlp>=2024.4.9
ffmpeg-python>=0.2.0
gtts>=2.5.1
yt-dlp>=2023.12.30
PyNaCl>=1.5.0
pydub>=0.25.0
ffmpeg-python>=0.2.0
pyttsx3>=2.90
gTTS>=2.4.0

# FFmpeg relacionados (mantener versiones específicas)
ffmpeg==1.4
ffmpeg-thumbnail>=0.1.0
python-ffmpeg-video-streaming>=0.1.16
static-ffmpeg>=2.7
imageio-ffmpeg>=0.5.1
ffmpeg-normalize>=1.30.0
ffmpeg-progress-yield>=0.11.1

# ==========================================
# WEB SERVER Y KEEP ALIVE
# ==========================================
flask>=2.3.0
gunicorn>=21.0.0

# ==========================================
# MONITOREO Y RENDIMIENTO
# ==========================================
psutil>=5.9.0
memory-profiler>=0.61.0
py-cpuinfo>=9.0.0

# ==========================================
# UTILIDADES DE FECHA Y TIEMPO
# ==========================================
python-dateutil>=2.8.0
pytz>=2023.3

# ==========================================
# LOGGING Y DEBUGGING
# ==========================================
colorlog>=6.7.0
rich>=13.0.0

# ==========================================
# SERIALIZACIÓN Y FORMATOS
# ==========================================
pyyaml>=6.0.0
toml>=0.10.0
msgpack>=1.0.0

# ==========================================
# GITHUB Y GIT INTEGRATION
# ==========================================
PyGithub>=1.59.0
GitPython>=3.1.0

# ==========================================
# CACHE Y OPTIMIZACIÓN
# ==========================================
cachetools>=5.3.0
diskcache>=5.6.0

# ==========================================
# COMPRESIÓN Y ARCHIVOS
# ==========================================
zipfile38>=0.0.3
rarfile>=4.0.0

# ==========================================
# TESTING (Opcional para desarrollo)
# ==========================================
pytest>=7.4.0
pytest-asyncio>=0.21.0

# ==========================================
# EXTRAS OPCIONALES (Descomenta si necesitas)
# ==========================================
 selenium>=4.15.0  # Para web scraping
 lxml>=4.9.0  # Parser XML/HTML rápido
 matplotlib>=3.7.0  # Para gráficos
 pandas>=2.1.0  # Para análisis de datos
 redis>=5.0.0  # Para cache distribuido
 celery>=5.3.0  # Para tareas asíncronas